import { Injectable, Logger } from '@nestjs/common';

export interface RerankingContext {
  query: string;
  userContext?: {
    technicalLevel?: string;
    previousInteractions?: string[];
    preferredCategories?: string[];
    budgetSensitive?: boolean;
  };
  queryIntent?: {
    primary: string;
    urgency: string;
    techLevel: string;
  };
  diversityWeight?: number; // 0-1, higher = more diversity
}

export interface EntitySimilarityScore {
  entityId: string;
  relevanceScore: number;
  diversityScore: number;
  interactionScore: number;
  qualityScore: number;
  finalScore: number;
}

@Injectable()
export class ContextualRerankingService {
  private readonly logger = new Logger(ContextualRerankingService.name);

  /**
   * Re-rank results using MMR and contextual factors
   */
  async rerankResults(
    entities: any[],
    context: RerankingContext,
    maxResults: number = 10,
  ): Promise<any[]> {
    if (entities.length === 0) {
      return entities;
    }

    this.logger.debug(`Re-ranking ${entities.length} entities with MMR and contextual factors`);

    try {
      // Step 1: Group entities by type for diversity
      const groupedEntities = this.groupByEntityType(entities);
      
      // Step 2: Apply MMR (Maximal Marginal Relevance) for diversity
      const diverseResults = this.applyMMR(entities, context, maxResults);
      
      // Step 3: Apply interaction-based boosting
      const boostedResults = this.applyInteractionBoost(diverseResults, context);
      
      // Step 4: Apply time-decay for stale entities
      const timeAdjustedResults = this.applyTimeDecay(boostedResults);
      
      // Step 5: Apply quality thresholds
      const qualityFilteredResults = this.applyQualityThresholds(timeAdjustedResults);

      this.logger.debug(`Re-ranking completed: ${entities.length} -> ${qualityFilteredResults.length} entities`);
      
      return qualityFilteredResults.slice(0, maxResults);

    } catch (error) {
      this.logger.error('Error in contextual re-ranking:', error.stack);
      // Return original results if re-ranking fails
      return entities.slice(0, maxResults);
    }
  }

  /**
   * Group entities by type for diversity analysis
   */
  private groupByEntityType(entities: any[]): Map<string, any[]> {
    const groups = new Map<string, any[]>();
    
    entities.forEach(entity => {
      const entityType = entity.entityType?.name || entity.entityTypeSlug || 'unknown';
      if (!groups.has(entityType)) {
        groups.set(entityType, []);
      }
      groups.get(entityType)!.push(entity);
    });

    return groups;
  }

  /**
   * Apply Maximal Marginal Relevance (MMR) algorithm for diversity
   */
  private applyMMR(
    entities: any[],
    context: RerankingContext,
    maxResults: number,
    lambda: number = 0.7, // Balance between relevance and diversity
  ): any[] {
    if (entities.length <= 1) {
      return entities;
    }

    const selected: any[] = [];
    const remaining = [...entities];
    
    // Adjust lambda based on context
    const adjustedLambda = this.adjustLambdaForContext(lambda, context);

    this.logger.debug(`Applying MMR with lambda=${adjustedLambda} for ${entities.length} entities`);

    while (remaining.length > 0 && selected.length < maxResults) {
      let bestScore = -Infinity;
      let bestIndex = -1;

      remaining.forEach((entity, index) => {
        const relevance = this.calculateRelevanceScore(entity, context);
        const diversity = this.calculateDiversityScore(entity, selected);
        const score = adjustedLambda * relevance + (1 - adjustedLambda) * diversity;

        if (score > bestScore) {
          bestScore = score;
          bestIndex = index;
        }
      });

      if (bestIndex !== -1) {
        selected.push(remaining[bestIndex]);
        remaining.splice(bestIndex, 1);
      } else {
        break;
      }
    }

    return selected;
  }

  /**
   * Adjust MMR lambda parameter based on context
   */
  private adjustLambdaForContext(baseLambda: number, context: RerankingContext): number {
    let adjustedLambda = baseLambda;

    // Increase relevance weight for specific needs
    if (context.queryIntent?.primary === 'specific_need') {
      adjustedLambda += 0.1;
    }

    // Increase diversity weight for exploration
    if (context.queryIntent?.primary === 'exploration') {
      adjustedLambda -= 0.1;
    }

    // Increase diversity weight if explicitly requested
    if (context.diversityWeight !== undefined) {
      adjustedLambda = 1 - context.diversityWeight;
    }

    return Math.max(0.3, Math.min(0.9, adjustedLambda));
  }

  /**
   * Calculate relevance score for an entity
   */
  private calculateRelevanceScore(entity: any, context: RerankingContext): number {
    let score = 0;

    // Base relevance from vector similarity (if available)
    if (entity.similarity) {
      score += entity.similarity * 0.4;
    }

    // Rating-based relevance
    if (entity.avgRating) {
      score += (entity.avgRating / 5) * 0.3;
    }

    // Review count indicates popularity
    if (entity.reviewCount) {
      const normalizedReviews = Math.min(entity.reviewCount / 100, 1);
      score += normalizedReviews * 0.2;
    }

    // Technical level match
    if (context.userContext?.technicalLevel && entity.technicalLevels) {
      const levelMatch = entity.technicalLevels.includes(context.userContext.technicalLevel);
      score += levelMatch ? 0.1 : 0;
    }

    return Math.max(0, Math.min(1, score));
  }

  /**
   * Calculate diversity score compared to already selected entities
   */
  private calculateDiversityScore(entity: any, selectedEntities: any[]): number {
    if (selectedEntities.length === 0) {
      return 1.0; // Maximum diversity for first entity
    }

    let diversityScore = 1.0;

    selectedEntities.forEach(selectedEntity => {
      // Entity type diversity
      if (entity.entityType?.name === selectedEntity.entityType?.name) {
        diversityScore -= 0.3;
      }

      // Category diversity
      const entityCategories = entity.categories?.map(c => c.category?.name) || [];
      const selectedCategories = selectedEntity.categories?.map(c => c.category?.name) || [];
      const categoryOverlap = entityCategories.filter(cat => selectedCategories.includes(cat)).length;
      
      if (categoryOverlap > 0) {
        diversityScore -= (categoryOverlap / Math.max(entityCategories.length, 1)) * 0.2;
      }

      // Feature diversity
      const entityFeatures = entity.features?.map(f => f.feature?.name) || [];
      const selectedFeatures = selectedEntity.features?.map(f => f.feature?.name) || [];
      const featureOverlap = entityFeatures.filter(feat => selectedFeatures.includes(feat)).length;
      
      if (featureOverlap > 0) {
        diversityScore -= (featureOverlap / Math.max(entityFeatures.length, 1)) * 0.1;
      }
    });

    return Math.max(0, diversityScore);
  }

  /**
   * Apply interaction-based boosting
   */
  private applyInteractionBoost(entities: any[], context: RerankingContext): any[] {
    if (!context.userContext?.previousInteractions) {
      return entities;
    }

    const previousInteractions = context.userContext.previousInteractions;

    return entities.map(entity => {
      let boostScore = 1.0;

      // Boost entities from previously interacted categories
      const entityCategories = entity.categories?.map(c => c.category?.name) || [];
      const hasInteractedCategory = entityCategories.some(cat => 
        previousInteractions.some(interaction => interaction.includes(cat))
      );

      if (hasInteractedCategory) {
        boostScore += 0.1;
      }

      // Boost entities of similar type to previously clicked ones
      const entityType = entity.entityType?.name;
      const hasInteractedType = previousInteractions.some(interaction => 
        interaction.includes(entityType)
      );

      if (hasInteractedType) {
        boostScore += 0.05;
      }

      return {
        ...entity,
        interactionBoost: boostScore,
        // Adjust the similarity or ranking score
        adjustedScore: (entity.similarity || 0.5) * boostScore,
      };
    }).sort((a, b) => (b.adjustedScore || 0) - (a.adjustedScore || 0));
  }

  /**
   * Apply time-decay for stale entities
   */
  private applyTimeDecay(entities: any[]): any[] {
    const now = new Date();
    
    return entities.map(entity => {
      let timeDecayFactor = 1.0;

      if (entity.updatedAt) {
        const entityDate = new Date(entity.updatedAt);
        const daysSinceUpdate = (now.getTime() - entityDate.getTime()) / (1000 * 60 * 60 * 24);
        
        // Apply gentle time decay (entities older than 365 days get reduced score)
        if (daysSinceUpdate > 365) {
          timeDecayFactor = 0.9;
        } else if (daysSinceUpdate > 180) {
          timeDecayFactor = 0.95;
        }
      }

      return {
        ...entity,
        timeDecayFactor,
        finalScore: (entity.adjustedScore || entity.similarity || 0.5) * timeDecayFactor,
      };
    }).sort((a, b) => (b.finalScore || 0) - (a.finalScore || 0));
  }

  /**
   * Apply quality thresholds to filter out low-quality results
   */
  private applyQualityThresholds(entities: any[]): any[] {
    return entities.filter(entity => {
      // Minimum similarity threshold
      const minSimilarity = 0.1;
      if (entity.similarity && entity.similarity < minSimilarity) {
        return false;
      }

      // Minimum rating threshold (if entity has ratings)
      if (entity.avgRating && entity.reviewCount > 5 && entity.avgRating < 2.0) {
        return false;
      }

      // Require basic entity information
      if (!entity.name || !entity.shortDescription) {
        return false;
      }

      return true;
    });
  }

  /**
   * Calculate entropy-based diversity score for the entire result set
   */
  calculateResultSetDiversity(entities: any[]): number {
    if (entities.length <= 1) {
      return 0;
    }

    // Calculate entity type distribution
    const typeDistribution = new Map<string, number>();
    entities.forEach(entity => {
      const entityType = entity.entityType?.name || 'unknown';
      typeDistribution.set(entityType, (typeDistribution.get(entityType) || 0) + 1);
    });

    // Calculate entropy
    let entropy = 0;
    const total = entities.length;
    
    typeDistribution.forEach(count => {
      const probability = count / total;
      entropy -= probability * Math.log2(probability);
    });

    // Normalize entropy (max entropy for uniform distribution)
    const maxEntropy = Math.log2(typeDistribution.size);
    return maxEntropy > 0 ? entropy / maxEntropy : 0;
  }
}
