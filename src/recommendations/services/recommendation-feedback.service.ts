import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';

export interface InteractionData {
  userId?: string;
  sessionId: string;
  query: string;
  recommendedEntityIds: string[];
  clickedEntityId?: string;
  dwellTime?: number; // Time spent on entity page in seconds
  position?: number; // Position of clicked entity in recommendations
  timestamp: Date;
  userAgent?: string;
  queryIntent?: string;
  diversityScore?: number;
}

export interface PersonalizationScore {
  entityId: string;
  userId: string;
  categoryPreference: number;
  typePreference: number;
  featurePreference: number;
  overallScore: number;
}

@Injectable()
export class RecommendationFeedbackService {
  private readonly logger = new Logger(RecommendationFeedbackService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Track user interaction with recommendations
   */
  async trackInteraction(interactionData: InteractionData): Promise<void> {
    try {
      this.logger.debug(`Tracking interaction for query: "${interactionData.query}"`);

      // Store the interaction in the database
      await this.prisma.recommendationInteraction.create({
        data: {
          userId: interactionData.userId,
          sessionId: interactionData.sessionId,
          query: interactionData.query,
          recommendedEntityIds: interactionData.recommendedEntityIds,
          clickedEntityId: interactionData.clickedEntityId,
          dwellTime: interactionData.dwellTime,
          position: interactionData.position,
          userAgent: interactionData.userAgent,
          queryIntent: interactionData.queryIntent,
          diversityScore: interactionData.diversityScore,
          createdAt: interactionData.timestamp,
        },
      });

      // Update entity popularity scores if there was a click
      if (interactionData.clickedEntityId) {
        await this.updateEntityScore(interactionData.clickedEntityId, 'click');
        
        // Update user preferences if user is logged in
        if (interactionData.userId) {
          await this.updateUserPreferences(interactionData.userId, interactionData.clickedEntityId);
        }
      }

      // Update query-entity associations
      if (interactionData.clickedEntityId) {
        await this.updateQueryEntityAssociations(interactionData.query, interactionData.clickedEntityId);
      }

      this.logger.debug(`Interaction tracked successfully`);

    } catch (error) {
      this.logger.error('Error tracking interaction:', error.stack);
      // Don't throw error to avoid breaking the main recommendation flow
    }
  }

  /**
   * Update entity popularity score based on interaction type
   */
  private async updateEntityScore(entityId: string, interactionType: 'click' | 'view' | 'dwell'): Promise<void> {
    try {
      const scoreIncrement = this.getScoreIncrement(interactionType);
      
      await this.prisma.entity.update({
        where: { id: entityId },
        data: {
          popularityScore: {
            increment: scoreIncrement,
          },
          updatedAt: new Date(),
        },
      });

      this.logger.debug(`Updated entity ${entityId} score by ${scoreIncrement}`);

    } catch (error) {
      this.logger.warn(`Failed to update entity score for ${entityId}:`, error.message);
    }
  }

  /**
   * Get score increment based on interaction type
   */
  private getScoreIncrement(interactionType: string): number {
    const scoreMap = {
      click: 1.0,
      view: 0.1,
      dwell: 0.5,
    };
    return scoreMap[interactionType] || 0;
  }

  /**
   * Update user preferences based on clicked entity
   */
  private async updateUserPreferences(userId: string, clickedEntityId: string): Promise<void> {
    try {
      // Get entity details to extract preferences
      const entity = await this.prisma.entity.findUnique({
        where: { id: clickedEntityId },
        include: {
          entityType: true,
          entityCategories: { include: { category: true } },
          entityFeatures: { include: { feature: true } },
        },
      });

      if (!entity) {
        this.logger.warn(`Entity ${clickedEntityId} not found for preference update`);
        return;
      }

      // Update or create user preferences
      const preferences = await this.prisma.userPreferences.upsert({
        where: { userId },
        create: {
          userId,
          preferredEntityTypes: [entity.entityType.slug],
          preferredCategories: entity.entityCategories.map(ec => ec.category.slug),
          preferredFeatures: entity.entityFeatures.map(ef => ef.feature.slug),
          interactionCount: 1,
          lastInteractionAt: new Date(),
        },
        update: {
          preferredEntityTypes: {
            push: entity.entityType.slug,
          },
          preferredCategories: {
            push: entity.entityCategories.map(ec => ec.category.slug),
          },
          preferredFeatures: {
            push: entity.entityFeatures.map(ef => ef.feature.slug),
          },
          interactionCount: {
            increment: 1,
          },
          lastInteractionAt: new Date(),
        },
      });

      this.logger.debug(`Updated preferences for user ${userId}`);

    } catch (error) {
      this.logger.warn(`Failed to update user preferences for ${userId}:`, error.message);
    }
  }

  /**
   * Update query-entity associations for better future recommendations
   */
  private async updateQueryEntityAssociations(query: string, entityId: string): Promise<void> {
    try {
      // Normalize query for consistent storage
      const normalizedQuery = query.toLowerCase().trim();

      await this.prisma.queryEntityAssociation.upsert({
        where: {
          query_entityId: {
            query: normalizedQuery,
            entityId,
          },
        },
        create: {
          query: normalizedQuery,
          entityId,
          clickCount: 1,
          lastClickedAt: new Date(),
        },
        update: {
          clickCount: {
            increment: 1,
          },
          lastClickedAt: new Date(),
        },
      });

      this.logger.debug(`Updated query-entity association for "${normalizedQuery}" -> ${entityId}`);

    } catch (error) {
      this.logger.warn(`Failed to update query-entity association:`, error.message);
    }
  }

  /**
   * Get personalized boost score for an entity based on user history
   */
  async getPersonalizedBoost(userId: string, entityId: string): Promise<number> {
    if (!userId) {
      return 1.0; // No boost for anonymous users
    }

    try {
      // Get user preferences
      const preferences = await this.prisma.userPreferences.findUnique({
        where: { userId },
      });

      if (!preferences) {
        return 1.0; // No boost if no preferences
      }

      // Get entity details
      const entity = await this.prisma.entity.findUnique({
        where: { id: entityId },
        include: {
          entityType: true,
          entityCategories: { include: { category: true } },
          entityFeatures: { include: { feature: true } },
        },
      });

      if (!entity) {
        return 1.0;
      }

      let boostScore = 1.0;

      // Entity type preference boost
      if (preferences.preferredEntityTypes.includes(entity.entityType.slug)) {
        boostScore += 0.2;
      }

      // Category preference boost
      const entityCategories = entity.entityCategories.map(ec => ec.category.slug);
      const categoryMatches = entityCategories.filter(cat => 
        preferences.preferredCategories.includes(cat)
      ).length;
      
      if (categoryMatches > 0) {
        boostScore += (categoryMatches / entityCategories.length) * 0.15;
      }

      // Feature preference boost
      const entityFeatures = entity.entityFeatures.map(ef => ef.feature.slug);
      const featureMatches = entityFeatures.filter(feat => 
        preferences.preferredFeatures.includes(feat)
      ).length;
      
      if (featureMatches > 0) {
        boostScore += (featureMatches / entityFeatures.length) * 0.1;
      }

      return Math.min(boostScore, 1.5); // Cap boost at 1.5x

    } catch (error) {
      this.logger.warn(`Failed to calculate personalized boost for user ${userId}:`, error.message);
      return 1.0;
    }
  }

  /**
   * Get popular entities based on recent interactions
   */
  async getPopularEntities(limit: number = 10, timeWindowDays: number = 30): Promise<string[]> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - timeWindowDays);

      const popularEntities = await this.prisma.recommendationInteraction.groupBy({
        by: ['clickedEntityId'],
        where: {
          clickedEntityId: { not: null },
          createdAt: { gte: cutoffDate },
        },
        _count: {
          clickedEntityId: true,
        },
        orderBy: {
          _count: {
            clickedEntityId: 'desc',
          },
        },
        take: limit,
      });

      return popularEntities
        .filter(item => item.clickedEntityId !== null)
        .map(item => item.clickedEntityId!);

    } catch (error) {
      this.logger.error('Error getting popular entities:', error.stack);
      return [];
    }
  }

  /**
   * Get recommendation quality metrics
   */
  async getQualityMetrics(timeWindowDays: number = 7): Promise<{
    clickThroughRate: number;
    averagePosition: number;
    zeroClickRate: number;
    averageDwellTime: number;
    totalInteractions: number;
  }> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - timeWindowDays);

      const interactions = await this.prisma.recommendationInteraction.findMany({
        where: {
          createdAt: { gte: cutoffDate },
        },
        select: {
          clickedEntityId: true,
          position: true,
          dwellTime: true,
        },
      });

      const totalInteractions = interactions.length;
      const clickedInteractions = interactions.filter(i => i.clickedEntityId !== null);
      const clickThroughRate = totalInteractions > 0 ? clickedInteractions.length / totalInteractions : 0;
      
      const zeroClickRate = totalInteractions > 0 ? 
        (totalInteractions - clickedInteractions.length) / totalInteractions : 0;

      const averagePosition = clickedInteractions.length > 0 ?
        clickedInteractions.reduce((sum, i) => sum + (i.position || 0), 0) / clickedInteractions.length : 0;

      const dwellTimes = interactions.filter(i => i.dwellTime !== null).map(i => i.dwellTime!);
      const averageDwellTime = dwellTimes.length > 0 ?
        dwellTimes.reduce((sum, time) => sum + time, 0) / dwellTimes.length : 0;

      return {
        clickThroughRate,
        averagePosition,
        zeroClickRate,
        averageDwellTime,
        totalInteractions,
      };

    } catch (error) {
      this.logger.error('Error calculating quality metrics:', error.stack);
      return {
        clickThroughRate: 0,
        averagePosition: 0,
        zeroClickRate: 0,
        averageDwellTime: 0,
        totalInteractions: 0,
      };
    }
  }

  /**
   * Check if model retraining should be triggered
   */
  async shouldRetrainModel(): Promise<boolean> {
    try {
      // Simple heuristic: retrain if we have enough new interactions
      const recentInteractions = await this.prisma.recommendationInteraction.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
        },
      });

      // Retrain if we have more than 100 interactions in the last day
      return recentInteractions > 100;

    } catch (error) {
      this.logger.error('Error checking retrain condition:', error.stack);
      return false;
    }
  }

  /**
   * Trigger model retraining (placeholder for ML pipeline)
   */
  async triggerModelRetraining(): Promise<void> {
    this.logger.log('Model retraining triggered - this would integrate with your ML pipeline');
    
    // In a real implementation, this would:
    // 1. Export recent interaction data
    // 2. Trigger ML model retraining
    // 3. Update model weights/parameters
    // 4. Deploy updated model
    
    // For now, just log the event
    try {
      await this.prisma.systemEvent.create({
        data: {
          eventType: 'MODEL_RETRAIN_TRIGGERED',
          description: 'Recommendation model retraining triggered based on interaction volume',
          metadata: {
            triggeredAt: new Date(),
            reason: 'interaction_threshold_reached',
          },
        },
      });
    } catch (error) {
      this.logger.warn('Failed to log retrain event:', error.message);
    }
  }
}
