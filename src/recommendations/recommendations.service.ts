import { Injectable, Logger, Inject } from '@nestjs/common';
import { EntitiesService } from '../entities/entities.service';
import { ILlmService, CandidateEntity } from '../common/llm/interfaces/llm.service.interface';
import { LlmFactoryService } from '../common/llm/services/llm-factory.service';
import { CreateRecommendationDto } from './dto/create-recommendation.dto';
import { RecommendationFiltersDto } from './dto/recommendation-filters.dto';
import { RecommendationResponseDto } from './dto/recommendation-response.dto';
import { EntityListItemResponseDto } from '../entities/dto/entity-list-item-response.dto';
import { ListEntitiesDto } from '../entities/dto/list-entities.dto';
import { VectorSearchDto } from '../entities/dto/vector-search.dto';
import { FilterExtractionService } from './services/filter-extraction.service';
import { QueryUnderstandingService } from './services/query-understanding.service';
import { AdvancedEntityRankingService, RankingContext } from '../common/ranking/advanced-entity-ranking.service';
import { PerformanceOptimizationService } from '../common/performance/performance-optimization.service';
import { QueryOptimizationService } from '../common/performance/query-optimization.service';

@Injectable()
export class RecommendationsService {
  private readonly logger = new Logger(RecommendationsService.name);

  constructor(
    private readonly entitiesService: EntitiesService,
    @Inject('ILlmService') private readonly llmService: ILlmService,
    private readonly llmFactoryService: LlmFactoryService,
    private readonly filterExtractionService: FilterExtractionService,
    private readonly queryUnderstandingService: QueryUnderstandingService,
    private readonly advancedRankingService: AdvancedEntityRankingService,
    private readonly performanceOptimizationService: PerformanceOptimizationService,
    private readonly queryOptimizationService: QueryOptimizationService,
  ) {}

  async getRecommendations(
    createRecommendationDto: CreateRecommendationDto,
  ): Promise<RecommendationResponseDto> {
    const { problem_description, filters } = createRecommendationDto;
    const maxCandidates = filters?.max_candidates || 50; // Increased from 20 to 50 for better coverage

    this.logger.log(
      `Getting enhanced recommendations for problem: "${problem_description}" with max ${maxCandidates} candidates`,
    );

    try {
      // Step 1: Enhanced query understanding and expansion
      const queryAnalysis = await this.queryUnderstandingService.analyzeAndExpandQuery(problem_description);

      this.logger.debug('Query analysis completed:', {
        intent: queryAnalysis.primaryIntent.primary,
        confidence: queryAnalysis.primaryIntent.confidence,
        conceptCount: Object.values(queryAnalysis.concepts).flat().length,
        variantCount: queryAnalysis.searchVariants.length
      });

      // Step 2: Optimized filter extraction with enhanced query context
      const extractedFilters = await this.performanceOptimizationService.optimizedFilterExtraction(
        problem_description,
        () => this.filterExtractionService.extractFiltersFromDescription(problem_description),
      );

      // Step 3: Enhance filters with query analysis insights
      const queryEnhancedFilters = this.enhanceFiltersWithQueryAnalysis(extractedFilters, queryAnalysis);

      // Step 4: Merge explicit filters with enhanced extracted filters (explicit takes precedence)
      const enhancedFilters = this.mergeFilters(queryEnhancedFilters, filters);

      this.logger.debug('Enhanced filters applied:', {
        extractedKeys: Object.keys(extractedFilters),
        explicitKeys: Object.keys(filters || {}),
        finalKeys: Object.keys(enhancedFilters),
      });

      // Step 3: Use enhanced filters to find candidate entities
      const candidateEntities = await this.findCandidateEntities(
        problem_description,
        enhancedFilters,
        maxCandidates,
      );

      this.logger.log(`Found ${candidateEntities.length} candidate entities with enhanced filtering`);

      if (candidateEntities.length === 0) {
        // Implement hybrid fallback strategy
        this.logger.warn('No candidates found with enhanced filtering, trying fallback strategies');

        const fallbackCandidates = await this.getFallbackCandidates(
          problem_description,
          enhancedFilters,
          maxCandidates,
        );

        if (fallbackCandidates.length === 0) {
          return {
            recommended_entities: [],
            explanation: 'No relevant entities found for your query. This might be because:\n' +
                        '• No entities match your specific filters\n' +
                        '• The search terms are too specific\n' +
                        '• No entities have been indexed for vector search yet\n\n' +
                        'Try:\n' +
                        '• Using broader search terms\n' +
                        '• Removing some filters\n' +
                        '• Searching for general categories like "AI tool" or "machine learning"',
            problem_description,
            candidates_analyzed: 0,
            llm_provider: 'N/A',
            generated_at: new Date(),
          };
        }

        // Use fallback candidates
        const llmCandidates = this.convertToLlmCandidates(fallbackCandidates);
        const llmRecommendation = await this.llmService.getRecommendation(
          problem_description,
          llmCandidates,
        );

        const recommendedEntities = await this.getRecommendedEntityDetails(
          llmRecommendation.recommendedEntityIds,
          fallbackCandidates,
        );

        return {
          recommended_entities: recommendedEntities,
          explanation: `${llmRecommendation.explanation}\n\n⚠️ Note: These recommendations were found using broader search criteria since no exact matches were found for your specific query.`,
          problem_description,
          candidates_analyzed: fallbackCandidates.length,
          llm_provider: 'FALLBACK',
          generated_at: new Date(),
        };
      }

      // Step 4: Convert entities to the format expected by LLM service
      const llmCandidates = this.convertToLlmCandidates(candidateEntities);

      // Step 5: Get LLM recommendations with enhanced context
      const llmRecommendation = await this.llmService.getRecommendation(
        problem_description,
        llmCandidates,
      );

      // Step 6: Fetch full entity details for recommended entities
      const recommendedEntities = await this.getRecommendedEntityDetails(
        llmRecommendation.recommendedEntityIds,
        candidateEntities,
      );

      // Step 7: Get current LLM provider for response metadata
      const currentProvider = await this.getCurrentLlmProvider();

      return {
        recommended_entities: recommendedEntities,
        explanation: llmRecommendation.explanation,
        problem_description,
        candidates_analyzed: candidateEntities.length,
        llm_provider: currentProvider,
        generated_at: new Date(),
      };
    } catch (error) {
      this.logger.error('Error generating enhanced recommendations', error.stack);
      throw error;
    }
  }

  private async findCandidateEntities(
    problemDescription: string,
    filters: RecommendationFiltersDto,
    maxCandidates: number,
  ): Promise<any[]> {
    this.logger.debug(`Finding candidates with filters:`, {
      filterKeys: Object.keys(filters || {}),
      maxCandidates,
    });

    // Step 1: Use vector search to find semantically relevant entities
    const vectorResults = await this.entitiesService.vectorSearch({
      query: problemDescription,
      limit: maxCandidates * 4, // Increased multiplier from 3 to 4 for better filtering options
    });

    if (vectorResults.length === 0) {
      this.logger.warn('No vector search results found');
      return [];
    }

    this.logger.debug(`Vector search found ${vectorResults.length} candidates`);

    // Step 2: Extract entity IDs from vector search results
    const entityIds = vectorResults.map(result => result.id);

    // Step 3: Build and optimize comprehensive ListEntitiesDto with ALL filters
    const baseFilters: ListEntitiesDto = {
      // Core parameters
      limit: maxCandidates * 2, // Allow for more results to maintain vector search order
      page: 1,

      // Copy ALL filters from RecommendationFiltersDto
      // This now includes 80+ filter parameters thanks to inheritance
      ...filters,
    };

    // Apply query optimization for better performance
    const optimizedFilters = this.queryOptimizationService.optimizeFilterOrder(baseFilters);
    const queryHints = this.queryOptimizationService.generateQueryHints(optimizedFilters);

    // Step 4: Execute optimized query with performance monitoring
    const queryStartTime = Date.now();
    const result = await this.entitiesService.findAll(optimizedFilters);
    const queryTime = Date.now() - queryStartTime;

    // Record query performance for optimization insights
    const querySignature = this.generateQuerySignature(optimizedFilters);
    this.queryOptimizationService.recordQueryPerformance(querySignature, queryTime);

    this.logger.debug(`After filtering: ${result.data.length} entities remain`);

    // Step 5: Maintain vector search relevance order while applying filters
    // Only include entities that passed both vector search AND filtering
    const filteredEntities = entityIds
      .map(id => result.data.find(entity => entity.id === id))
      .filter(entity => entity !== undefined);

    // Step 6: Apply optimized advanced ranking
    const rankingContext: RankingContext = {
      appliedFilters: optimizedFilters,
      filterConfidence: this.extractFilterConfidence(filters),
      userPreferences: this.extractUserPreferences(filters),
      currentResults: [],
    };

    const rankedEntities = await this.performanceOptimizationService.optimizedEntityRanking(
      filteredEntities,
      rankingContext,
      () => this.advancedRankingService.rankEntities(filteredEntities, rankingContext),
    );

    const finalEntities = rankedEntities.slice(0, maxCandidates);

    this.logger.debug(`Advanced ranking completed`, {
      filteredCount: filteredEntities.length,
      finalCount: finalEntities.length,
      topScore: finalEntities[0]?.rankingScore,
      avgScore: finalEntities.reduce((sum, e) => sum + e.rankingScore, 0) / finalEntities.length,
    });

    return finalEntities;
  }

  /**
   * Merge extracted filters with explicit filters, giving precedence to explicit filters
   */
  private mergeFilters(
    extractedFilters: Partial<RecommendationFiltersDto>,
    explicitFilters?: RecommendationFiltersDto,
  ): RecommendationFiltersDto {
    const merged: RecommendationFiltersDto = {
      ...extractedFilters,
      ...explicitFilters, // Explicit filters override extracted ones
    };

    // Special handling for array fields - combine rather than override
    if (extractedFilters.entityTypeIds && explicitFilters?.entityTypeIds) {
      merged.entityTypeIds = [...new Set([...extractedFilters.entityTypeIds, ...explicitFilters.entityTypeIds])];
    }

    if (extractedFilters.technical_levels && explicitFilters?.technical_levels) {
      merged.technical_levels = [...new Set([...extractedFilters.technical_levels, ...explicitFilters.technical_levels])];
    }

    if (extractedFilters.platforms && explicitFilters?.platforms) {
      merged.platforms = [...new Set([...extractedFilters.platforms, ...explicitFilters.platforms])];
    }

    if (extractedFilters.frameworks && explicitFilters?.frameworks) {
      merged.frameworks = [...new Set([...extractedFilters.frameworks, ...explicitFilters.frameworks])];
    }

    return merged;
  }

  /**
   * Extract filter confidence scores for ranking
   */
  private extractFilterConfidence(filters: RecommendationFiltersDto): Record<string, number> {
    const confidence: Record<string, number> = {};

    // High confidence for explicit filters
    Object.keys(filters).forEach(key => {
      const value = (filters as any)[key];
      if (value !== undefined && value !== null) {
        confidence[key] = 0.9; // High confidence for explicit filters
      }
    });

    return confidence;
  }

  /**
   * Extract user preferences for ranking context
   */
  private extractUserPreferences(filters: RecommendationFiltersDto): any {
    return {
      technical_level: filters.technical_levels?.[0],
      budget: filters.has_free_tier ? 'free' :
              filters.price_range === 'LOW' ? 'low' :
              filters.price_range === 'MEDIUM' ? 'medium' : 'high',
      preferred_categories: [], // Could be enhanced with user profile data
      excluded_categories: [],
    };
  }

  /**
   * Generate a signature for query performance tracking
   */
  private generateQuerySignature(filters: ListEntitiesDto): string {
    const keyFilters = {
      entityTypes: filters.entityTypeIds?.length || 0,
      hasSearch: !!filters.searchTerm,
      categories: filters.categoryIds?.length || 0,
      techLevels: filters.technical_levels?.length || 0,
      hasFreeTier: filters.has_free_tier,
      hasApi: filters.has_api,
      priceRange: filters.price_range ? 1 : 0,
    };

    return `query:${JSON.stringify(keyFilters)}`;
  }

  private convertToLlmCandidates(entities: any[]): CandidateEntity[] {
    return entities.map((entity) => ({
      id: entity.id,
      name: entity.name,
      shortDescription: entity.shortDescription,
      description: entity.description,
      entityType: {
        name: entity.entityType.name,
        slug: entity.entityType.slug,
      },
      categories: entity.entityCategories || [],
      tags: entity.entityTags || [],
      features: entity.entityFeatures || [],
      websiteUrl: entity.websiteUrl,
      logoUrl: entity.logoUrl,
      avgRating: entity.avgRating,
      reviewCount: entity.reviewCount,
    }));
  }

  private async getRecommendedEntityDetails(
    recommendedIds: string[],
    candidateEntities: any[],
  ): Promise<EntityListItemResponseDto[]> {
    // Filter candidate entities to only include recommended ones, maintaining order
    const recommendedEntities = recommendedIds
      .map((id) => candidateEntities.find((entity) => entity.id === id))
      .filter((entity) => entity !== undefined);

    // Convert to response DTOs (you may need to implement this mapping)
    return recommendedEntities.map((entity) => this.mapToEntityListItemResponseDto(entity));
  }

  private mapToEntityListItemResponseDto(entity: any): EntityListItemResponseDto {
    // This should match the mapping logic from EntitiesController
    const listItemDto = new EntityListItemResponseDto();
    listItemDto.id = entity.id;
    listItemDto.name = entity.name;
    listItemDto.slug = entity.slug;
    listItemDto.logoUrl = entity.logoUrl;
    listItemDto.shortDescription = entity.shortDescription;
    listItemDto.websiteUrl = entity.websiteUrl;
    listItemDto.entityType = {
      name: entity.entityType.name,
      slug: entity.entityType.slug,
    };
    listItemDto.avgRating = entity.avgRating;
    listItemDto.reviewCount = entity.reviewCount;
    listItemDto.saveCount = entity._count?.userSavedEntities ?? 0;

    if (entity.entityType?.slug === 'ai-tool' && entity.entityDetailsTool) {
      listItemDto.hasFreeTier = entity.entityDetailsTool.hasFreeTier;
    }

    return listItemDto;
  }

  private async getCurrentLlmProvider(): Promise<string> {
    try {
      // Get the current provider from the factory service
      const providers = this.llmFactoryService.getAvailableProviders();
      // For now, we'll determine the provider by checking which service is being used
      // In a more sophisticated implementation, you might want to expose this from the factory
      return 'OPENAI'; // Default assumption, could be enhanced
    } catch (error) {
      this.logger.warn('Could not determine current LLM provider', error.message);
      return 'UNKNOWN';
    }
  }

  /**
   * Hybrid fallback strategy when no candidates are found
   */
  private async getFallbackCandidates(
    problemDescription: string,
    originalFilters: RecommendationFiltersDto,
    maxCandidates: number,
  ): Promise<any[]> {
    this.logger.debug('Executing fallback candidate search strategies');

    // Strategy 1: Try vector search with lower threshold
    let candidates = await this.tryLowerThresholdVectorSearch(problemDescription, maxCandidates);
    if (candidates.length > 0) {
      this.logger.debug(`Fallback Strategy 1 (lower threshold): Found ${candidates.length} candidates`);
      return candidates;
    }

    // Strategy 2: Try without entity type filters
    if (originalFilters.entityTypeIds?.length || originalFilters.entity_type_ids?.length) {
      candidates = await this.tryWithoutEntityTypeFilters(problemDescription, originalFilters, maxCandidates);
      if (candidates.length > 0) {
        this.logger.debug(`Fallback Strategy 2 (no entity type filter): Found ${candidates.length} candidates`);
        return candidates;
      }
    }

    // Strategy 3: Try keyword-based search instead of vector search
    candidates = await this.tryKeywordBasedSearch(problemDescription, originalFilters, maxCandidates);
    if (candidates.length > 0) {
      this.logger.debug(`Fallback Strategy 3 (keyword search): Found ${candidates.length} candidates`);
      return candidates;
    }

    // Strategy 4: Try with minimal filters (only status and basic criteria)
    candidates = await this.tryMinimalFilters(problemDescription, maxCandidates);
    if (candidates.length > 0) {
      this.logger.debug(`Fallback Strategy 4 (minimal filters): Found ${candidates.length} candidates`);
      return candidates;
    }

    this.logger.warn('All fallback strategies failed to find candidates');
    return [];
  }

  /**
   * Strategy 1: Try vector search with lower similarity threshold
   */
  private async tryLowerThresholdVectorSearch(problemDescription: string, maxCandidates: number): Promise<any[]> {
    try {
      // For now, we'll use the regular vector search since we don't have vectorSearchWithThreshold
      // But we can implement keyword-based fallback
      const vectorResults = await this.entitiesService.vectorSearch({
        query: problemDescription,
        limit: maxCandidates * 5, // Get more results to increase chances
      });

      if (vectorResults.length === 0) {
        return [];
      }

      const entityIds = vectorResults.map(result => result.id);
      const result = await this.entitiesService.findAll({
        limit: maxCandidates * 2,
        page: 1,
        status: 'ACTIVE',
      });

      return entityIds
        .map(id => result.data.find(entity => entity.id === id))
        .filter(entity => entity !== undefined)
        .slice(0, maxCandidates);
    } catch (error) {
      this.logger.warn('Lower threshold vector search failed', error.message);
      return [];
    }
  }

  /**
   * Strategy 2: Try without entity type filters
   */
  private async tryWithoutEntityTypeFilters(
    problemDescription: string,
    originalFilters: RecommendationFiltersDto,
    maxCandidates: number,
  ): Promise<any[]> {
    try {
      const vectorResults = await this.entitiesService.vectorSearch({
        query: problemDescription,
        limit: maxCandidates * 3,
      });

      if (vectorResults.length === 0) {
        return [];
      }

      const entityIds = vectorResults.map(result => result.id);
      const filtersWithoutEntityType = { ...originalFilters };
      delete filtersWithoutEntityType.entityTypeIds;
      delete filtersWithoutEntityType.entity_type_ids;

      const result = await this.entitiesService.findAll({
        limit: maxCandidates * 2,
        page: 1,
        ...filtersWithoutEntityType,
      });

      return entityIds
        .map(id => result.data.find(entity => entity.id === id))
        .filter(entity => entity !== undefined)
        .slice(0, maxCandidates);
    } catch (error) {
      this.logger.warn('Search without entity type filters failed', error.message);
      return [];
    }
  }

  /**
   * Strategy 3: Try keyword-based search instead of vector search
   */
  private async tryKeywordBasedSearch(
    problemDescription: string,
    originalFilters: RecommendationFiltersDto,
    maxCandidates: number,
  ): Promise<any[]> {
    try {
      // Extract keywords from the problem description
      const keywords = this.extractKeywords(problemDescription);

      if (keywords.length === 0) {
        return [];
      }

      // Use the most relevant keyword as searchTerm
      const searchTerm = keywords[0];

      const result = await this.entitiesService.findAll({
        limit: maxCandidates * 2,
        page: 1,
        searchTerm,
        status: 'ACTIVE',
        ...originalFilters,
      });

      return result.data.slice(0, maxCandidates);
    } catch (error) {
      this.logger.warn('Keyword-based search failed', error.message);
      return [];
    }
  }

  /**
   * Strategy 4: Try with minimal filters (only status and basic criteria)
   */
  private async tryMinimalFilters(problemDescription: string, maxCandidates: number): Promise<any[]> {
    try {
      // Extract basic entity type from description
      const extractedFilters = await this.filterExtractionService.extractFiltersFromDescription(problemDescription);

      const result = await this.entitiesService.findAll({
        limit: maxCandidates * 2,
        page: 1,
        status: 'ACTIVE',
        entityTypeIds: extractedFilters.entityTypeIds, // Only use extracted entity types
      });

      return result.data.slice(0, maxCandidates);
    } catch (error) {
      this.logger.warn('Minimal filters search failed', error.message);
      return [];
    }
  }

  /**
   * Extract keywords from problem description for fallback search
   */
  private extractKeywords(description: string): string[] {
    const stopWords = new Set(['i', 'need', 'want', 'looking', 'for', 'to', 'help', 'me', 'with', 'a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'by', 'from']);

    return description
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word))
      .slice(0, 5); // Take top 5 keywords
  }

  /**
   * Debug method to check recommendation pipeline
   */
  async debugRecommendationPipeline(problemDescription: string): Promise<any> {
    const debug: any = {
      step1_embedding_generation: null,
      step2_vector_search: null,
      step3_embedding_coverage: null,
      step4_filter_extraction: null,
      step5_basic_search: null,
    };

    try {
      // Step 1: Check if we can generate embeddings
      debug.step1_embedding_generation = await this.entitiesService.debugEmbeddingGeneration(problemDescription);

      // Step 2: Check vector search
      debug.step2_vector_search = await this.entitiesService.vectorSearch({
        query: problemDescription,
        limit: 10,
      });

      // Step 3: Check embedding coverage
      debug.step3_embedding_coverage = await this.entitiesService.getEmbeddingCoverage();

      // Step 4: Check filter extraction
      debug.step4_filter_extraction = await this.filterExtractionService.extractFiltersFromDescription(problemDescription);

      // Step 5: Check basic search without filters
      const basicSearch = await this.entitiesService.findAll({
        limit: 10,
        page: 1,
        status: 'ACTIVE',
      });
      debug.step5_basic_search = {
        total_entities: basicSearch.total,
        returned_entities: basicSearch.data.length,
        sample_entities: basicSearch.data.slice(0, 3).map(e => ({ id: e.id, name: e.name, entityTypeId: e.entityTypeId })),
      };

      return debug;
    } catch (error) {
      return {
        ...debug,
        error: error.message,
        stack: error.stack,
      };
    }
  }
}
