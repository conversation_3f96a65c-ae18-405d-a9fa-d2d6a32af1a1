import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';

@Injectable()
export class OpenaiService {
  private readonly openai: OpenAI;
  private readonly logger = new Logger(OpenaiService.name);

  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY is not set in environment variables.');
    }
    this.openai = new OpenAI({ apiKey });
  }

  async generateEmbedding(text: string): Promise<number[] | null> {
    if (!text) {
      this.logger.warn('generateEmbedding called with empty text.');
      return null;
    }

    try {
      const response = await this.openai.embeddings.create({
        model: 'text-embedding-3-large', // Upgraded for better accuracy
        input: text.trim(),
        dimensions: 1536, // Higher dimensions for better discrimination
      });

      if (response.data && response.data.length > 0 && response.data[0].embedding) {
        return response.data[0].embedding;
      } else {
        this.logger.warn('OpenAI API returned no embedding for the provided text.');
        return null;
      }
    } catch (error) {
      this.logger.error('Error generating embedding from OpenAI', error.stack);
      // Re-throwing the error as the user wants embedding failures to fail the transaction
      throw error;
    }
  }

  async generateCompletion(prompt: string): Promise<string> {
    if (!prompt) {
      this.logger.warn('generateCompletion called with empty prompt.');
      return '';
    }

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        max_tokens: 1000,
        temperature: 0.7,
      });

      if (response.choices && response.choices.length > 0 && response.choices[0].message?.content) {
        return response.choices[0].message.content;
      } else {
        this.logger.warn('OpenAI API returned no completion for the provided prompt.');
        return '';
      }
    } catch (error) {
      this.logger.error('Error generating completion from OpenAI', error.stack);
      throw error;
    }
  }
}