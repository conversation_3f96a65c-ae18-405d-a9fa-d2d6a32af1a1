import { Injectable, Logger } from '@nestjs/common';
import { OpenaiService } from '../../openai/openai.service';

export interface EntityEmbeddingContext {
  id: string;
  name: string;
  shortDescription?: string;
  description?: string;
  categories: Array<{ category: { name: string } }>;
  features: Array<{ feature: { name: string } }>;
  tags: Array<{ tag: { name: string } }>;
  entityType: { name: string };
  useCases?: string[];
  targetAudience?: string[];
  platforms?: string[];
  integrations?: string[];
}

@Injectable()
export class EnhancedEmbeddingService {
  private readonly logger = new Logger(EnhancedEmbeddingService.name);

  constructor(private readonly openaiService: OpenaiService) {}

  /**
   * Generate enhanced embedding with enriched context from multiple entity fields
   */
  async generateHybridEmbedding(entity: EntityEmbeddingContext): Promise<number[] | null> {
    try {
      // Step 1: Create enriched text representation
      const enrichedText = this.buildEnrichedTextRepresentation(entity);
      
      this.logger.debug(`Generating enhanced embedding for entity: ${entity.name}`);
      this.logger.debug(`Enriched text length: ${enrichedText.length} characters`);

      // Step 2: Generate embedding with enriched context
      const embedding = await this.openaiService.generateEmbedding(enrichedText);
      
      if (!embedding) {
        this.logger.warn(`Failed to generate embedding for entity: ${entity.name}`);
        return null;
      }

      this.logger.debug(`Successfully generated enhanced embedding for: ${entity.name}, dimensions: ${embedding.length}`);
      return embedding;

    } catch (error) {
      this.logger.error(`Error generating enhanced embedding for entity ${entity.name}:`, error.stack);
      return null;
    }
  }

  /**
   * Build enriched text representation by concatenating multiple entity fields
   */
  private buildEnrichedTextRepresentation(entity: EntityEmbeddingContext): string {
    const sections = [];

    // Core identity
    sections.push(`${entity.name}`);
    
    if (entity.shortDescription) {
      sections.push(`Description: ${entity.shortDescription}`);
    }
    
    if (entity.description && entity.description !== entity.shortDescription) {
      sections.push(`Details: ${entity.description}`);
    }

    // Entity type and categorization
    sections.push(`Type: ${entity.entityType.name}`);
    
    if (entity.categories?.length > 0) {
      const categoryNames = entity.categories.map(c => c.category.name).join(', ');
      sections.push(`Categories: ${categoryNames}`);
    }

    // Features and capabilities
    if (entity.features?.length > 0) {
      const featureNames = entity.features.map(f => f.feature.name).join(', ');
      sections.push(`Features: ${featureNames}`);
    }

    // Tags for additional context
    if (entity.tags?.length > 0) {
      const tagNames = entity.tags.map(t => t.tag.name).join(', ');
      sections.push(`Tags: ${tagNames}`);
    }

    // Use cases for semantic understanding
    if (entity.useCases?.length > 0) {
      sections.push(`Use cases: ${entity.useCases.join(', ')}`);
    }

    // Target audience for better matching
    if (entity.targetAudience?.length > 0) {
      sections.push(`Target audience: ${entity.targetAudience.join(', ')}`);
    }

    // Platform and integration context
    if (entity.platforms?.length > 0) {
      sections.push(`Platforms: ${entity.platforms.join(', ')}`);
    }

    if (entity.integrations?.length > 0) {
      sections.push(`Integrations: ${entity.integrations.join(', ')}`);
    }

    // Join all sections with proper spacing
    const enrichedText = sections.filter(section => section.trim().length > 0).join('\n');
    
    // Ensure we don't exceed token limits (roughly 8000 characters for safety)
    if (enrichedText.length > 8000) {
      this.logger.warn(`Enriched text too long (${enrichedText.length} chars), truncating for entity: ${entity.name}`);
      return enrichedText.substring(0, 8000);
    }

    return enrichedText;
  }

  /**
   * Generate embedding for search queries with query expansion
   */
  async generateQueryEmbedding(query: string, expandedTerms?: string[]): Promise<number[] | null> {
    try {
      let enhancedQuery = query;

      // Add expanded terms if provided
      if (expandedTerms && expandedTerms.length > 0) {
        enhancedQuery = `${query} ${expandedTerms.join(' ')}`;
      }

      this.logger.debug(`Generating query embedding for: "${query}"`);
      if (expandedTerms?.length > 0) {
        this.logger.debug(`With expanded terms: ${expandedTerms.join(', ')}`);
      }

      const embedding = await this.openaiService.generateEmbedding(enhancedQuery);
      
      if (!embedding) {
        this.logger.warn(`Failed to generate query embedding for: "${query}"`);
        return null;
      }

      return embedding;

    } catch (error) {
      this.logger.error(`Error generating query embedding for "${query}":`, error.stack);
      return null;
    }
  }

  /**
   * Calculate semantic similarity between two embeddings
   */
  calculateCosineSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embeddings must have the same dimensions');
    }

    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      norm1 += embedding1[i] * embedding1[i];
      norm2 += embedding2[i] * embedding2[i];
    }

    const similarity = dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    return Math.max(0, Math.min(1, similarity)); // Clamp between 0 and 1
  }

  /**
   * Batch generate embeddings for multiple entities
   */
  async batchGenerateEmbeddings(
    entities: EntityEmbeddingContext[],
    batchSize: number = 10,
    delayMs: number = 100
  ): Promise<Array<{ entityId: string; embedding: number[] | null }>> {
    const results: Array<{ entityId: string; embedding: number[] | null }> = [];
    
    this.logger.log(`Starting batch embedding generation for ${entities.length} entities`);

    for (let i = 0; i < entities.length; i += batchSize) {
      const batch = entities.slice(i, i + batchSize);
      
      this.logger.debug(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(entities.length / batchSize)}`);

      const batchPromises = batch.map(async (entity) => {
        const embedding = await this.generateHybridEmbedding(entity);
        return { entityId: entity.id, embedding };
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Add delay between batches to respect rate limits
      if (i + batchSize < entities.length && delayMs > 0) {
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }
    }

    const successCount = results.filter(r => r.embedding !== null).length;
    this.logger.log(`Batch embedding generation completed: ${successCount}/${entities.length} successful`);

    return results;
  }
}
