"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var RecommendationsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecommendationsService = void 0;
const common_1 = require("@nestjs/common");
const entities_service_1 = require("../entities/entities.service");
const llm_factory_service_1 = require("../common/llm/services/llm-factory.service");
const entity_list_item_response_dto_1 = require("../entities/dto/entity-list-item-response.dto");
const filter_extraction_service_1 = require("./services/filter-extraction.service");
const query_understanding_service_1 = require("./services/query-understanding.service");
const contextual_reranking_service_1 = require("./services/contextual-reranking.service");
const recommendation_feedback_service_1 = require("./services/recommendation-feedback.service");
const advanced_entity_ranking_service_1 = require("../common/ranking/advanced-entity-ranking.service");
const performance_optimization_service_1 = require("../common/performance/performance-optimization.service");
const query_optimization_service_1 = require("../common/performance/query-optimization.service");
let RecommendationsService = RecommendationsService_1 = class RecommendationsService {
    constructor(entitiesService, llmService, llmFactoryService, filterExtractionService, queryUnderstandingService, contextualRerankingService, feedbackService, advancedRankingService, performanceOptimizationService, queryOptimizationService) {
        this.entitiesService = entitiesService;
        this.llmService = llmService;
        this.llmFactoryService = llmFactoryService;
        this.filterExtractionService = filterExtractionService;
        this.queryUnderstandingService = queryUnderstandingService;
        this.contextualRerankingService = contextualRerankingService;
        this.feedbackService = feedbackService;
        this.advancedRankingService = advancedRankingService;
        this.performanceOptimizationService = performanceOptimizationService;
        this.queryOptimizationService = queryOptimizationService;
        this.logger = new common_1.Logger(RecommendationsService_1.name);
    }
    async getRecommendations(createRecommendationDto) {
        const { problem_description, filters } = createRecommendationDto;
        const maxCandidates = filters?.max_candidates || 50;
        this.logger.log(`Getting enhanced recommendations for problem: "${problem_description}" with max ${maxCandidates} candidates`);
        try {
            const queryAnalysis = await this.queryUnderstandingService.analyzeAndExpandQuery(problem_description);
            this.logger.debug('Query analysis completed:', {
                intent: queryAnalysis.primaryIntent.primary,
                confidence: queryAnalysis.primaryIntent.confidence,
                conceptCount: Object.values(queryAnalysis.concepts).flat().length,
                variantCount: queryAnalysis.searchVariants.length
            });
            const extractedFilters = await this.performanceOptimizationService.optimizedFilterExtraction(problem_description, () => this.filterExtractionService.extractFiltersFromDescription(problem_description));
            const queryEnhancedFilters = this.enhanceFiltersWithQueryAnalysis(extractedFilters, queryAnalysis);
            const enhancedFilters = this.mergeFilters(queryEnhancedFilters, filters);
            this.logger.debug('Enhanced filters applied:', {
                extractedKeys: Object.keys(extractedFilters),
                queryEnhancedKeys: Object.keys(queryEnhancedFilters),
                explicitKeys: Object.keys(filters || {}),
                finalKeys: Object.keys(enhancedFilters),
            });
            const candidateEntities = await this.findCandidateEntitiesWithQueryVariants(problem_description, queryAnalysis.searchVariants, enhancedFilters, maxCandidates);
            this.logger.log(`Found ${candidateEntities.length} candidate entities with enhanced filtering`);
            if (candidateEntities.length === 0) {
                this.logger.warn('No candidates found with enhanced filtering, trying fallback strategies');
                const fallbackCandidates = await this.getFallbackCandidates(problem_description, enhancedFilters, maxCandidates);
                if (fallbackCandidates.length === 0) {
                    return {
                        recommended_entities: [],
                        explanation: 'No relevant entities found for your query. This might be because:\n' +
                            '• No entities match your specific filters\n' +
                            '• The search terms are too specific\n' +
                            '• No entities have been indexed for vector search yet\n\n' +
                            'Try:\n' +
                            '• Using broader search terms\n' +
                            '• Removing some filters\n' +
                            '• Searching for general categories like "AI tool" or "machine learning"',
                        problem_description,
                        candidates_analyzed: 0,
                        llm_provider: 'N/A',
                        generated_at: new Date(),
                    };
                }
                const llmCandidates = this.convertToLlmCandidates(fallbackCandidates);
                const llmRecommendation = await this.llmService.getRecommendation(problem_description, llmCandidates);
                const recommendedEntities = await this.getRecommendedEntityDetails(llmRecommendation.recommendedEntityIds, fallbackCandidates);
                return {
                    recommended_entities: recommendedEntities,
                    explanation: `${llmRecommendation.explanation}\n\n⚠️ Note: These recommendations were found using broader search criteria since no exact matches were found for your specific query.`,
                    problem_description,
                    candidates_analyzed: fallbackCandidates.length,
                    llm_provider: 'FALLBACK',
                    generated_at: new Date(),
                };
            }
            const llmCandidates = this.convertToLlmCandidates(candidateEntities);
            const llmRecommendation = await this.llmService.getRecommendation(problem_description, llmCandidates);
            const recommendedEntities = await this.getRecommendedEntityDetails(llmRecommendation.recommendedEntityIds, candidateEntities);
            const rerankingContext = {
                query: problem_description,
                queryIntent: queryAnalysis.primaryIntent,
                userContext: {
                    technicalLevel: queryAnalysis.primaryIntent.techLevel,
                    budgetSensitive: queryAnalysis.implicitNeeds.budgetSensitive,
                },
                diversityWeight: this.calculateDiversityWeight(queryAnalysis.primaryIntent),
            };
            const rerankedEntities = await this.contextualRerankingService.rerankResults(recommendedEntities, rerankingContext, 10);
            const currentProvider = await this.getCurrentLlmProvider();
            const diversityScore = this.contextualRerankingService.calculateResultSetDiversity(rerankedEntities);
            return {
                recommended_entities: rerankedEntities,
                explanation: `${llmRecommendation.explanation}\n\n✨ Results optimized for relevance and diversity (diversity score: ${(diversityScore * 100).toFixed(1)}%).`,
                problem_description,
                candidates_analyzed: candidateEntities.length,
                llm_provider: currentProvider,
                generated_at: new Date(),
                metadata: {
                    diversity_score: diversityScore,
                    reranking_applied: true,
                },
            };
        }
        catch (error) {
            this.logger.error('Error generating enhanced recommendations', error.stack);
            throw error;
        }
    }
    async findCandidateEntities(problemDescription, filters, maxCandidates) {
        this.logger.debug(`Finding candidates with filters:`, {
            filterKeys: Object.keys(filters || {}),
            maxCandidates,
        });
        const vectorResults = await this.entitiesService.vectorSearch({
            query: problemDescription,
            limit: maxCandidates * 4,
        });
        if (vectorResults.length === 0) {
            this.logger.warn('No vector search results found');
            return [];
        }
        this.logger.debug(`Vector search found ${vectorResults.length} candidates`);
        const entityIds = vectorResults.map(result => result.id);
        const baseFilters = {
            limit: maxCandidates * 2,
            page: 1,
            ...filters,
        };
        const optimizedFilters = this.queryOptimizationService.optimizeFilterOrder(baseFilters);
        const queryHints = this.queryOptimizationService.generateQueryHints(optimizedFilters);
        const queryStartTime = Date.now();
        const result = await this.entitiesService.findAll(optimizedFilters);
        const queryTime = Date.now() - queryStartTime;
        const querySignature = this.generateQuerySignature(optimizedFilters);
        this.queryOptimizationService.recordQueryPerformance(querySignature, queryTime);
        this.logger.debug(`After filtering: ${result.data.length} entities remain`);
        const filteredEntities = entityIds
            .map(id => result.data.find(entity => entity.id === id))
            .filter(entity => entity !== undefined);
        const rankingContext = {
            appliedFilters: optimizedFilters,
            filterConfidence: this.extractFilterConfidence(filters),
            userPreferences: this.extractUserPreferences(filters),
            currentResults: [],
        };
        const rankedEntities = await this.performanceOptimizationService.optimizedEntityRanking(filteredEntities, rankingContext, () => this.advancedRankingService.rankEntities(filteredEntities, rankingContext));
        const finalEntities = rankedEntities.slice(0, maxCandidates);
        this.logger.debug(`Advanced ranking completed`, {
            filteredCount: filteredEntities.length,
            finalCount: finalEntities.length,
            topScore: finalEntities[0]?.rankingScore,
            avgScore: finalEntities.reduce((sum, e) => sum + e.rankingScore, 0) / finalEntities.length,
        });
        return finalEntities;
    }
    mergeFilters(extractedFilters, explicitFilters) {
        const merged = {
            ...extractedFilters,
            ...explicitFilters,
        };
        if (extractedFilters.entityTypeIds && explicitFilters?.entityTypeIds) {
            merged.entityTypeIds = [...new Set([...extractedFilters.entityTypeIds, ...explicitFilters.entityTypeIds])];
        }
        if (extractedFilters.technical_levels && explicitFilters?.technical_levels) {
            merged.technical_levels = [...new Set([...extractedFilters.technical_levels, ...explicitFilters.technical_levels])];
        }
        if (extractedFilters.platforms && explicitFilters?.platforms) {
            merged.platforms = [...new Set([...extractedFilters.platforms, ...explicitFilters.platforms])];
        }
        if (extractedFilters.frameworks && explicitFilters?.frameworks) {
            merged.frameworks = [...new Set([...extractedFilters.frameworks, ...explicitFilters.frameworks])];
        }
        return merged;
    }
    extractFilterConfidence(filters) {
        const confidence = {};
        Object.keys(filters).forEach(key => {
            const value = filters[key];
            if (value !== undefined && value !== null) {
                confidence[key] = 0.9;
            }
        });
        return confidence;
    }
    extractUserPreferences(filters) {
        return {
            technical_level: filters.technical_levels?.[0],
            budget: filters.has_free_tier ? 'free' :
                filters.price_range === 'LOW' ? 'low' :
                    filters.price_range === 'MEDIUM' ? 'medium' : 'high',
            preferred_categories: [],
            excluded_categories: [],
        };
    }
    generateQuerySignature(filters) {
        const keyFilters = {
            entityTypes: filters.entityTypeIds?.length || 0,
            hasSearch: !!filters.searchTerm,
            categories: filters.categoryIds?.length || 0,
            techLevels: filters.technical_levels?.length || 0,
            hasFreeTier: filters.has_free_tier,
            hasApi: filters.has_api,
            priceRange: filters.price_range ? 1 : 0,
        };
        return `query:${JSON.stringify(keyFilters)}`;
    }
    convertToLlmCandidates(entities) {
        return entities.map((entity) => ({
            id: entity.id,
            name: entity.name,
            shortDescription: entity.shortDescription,
            description: entity.description,
            entityType: {
                name: entity.entityType.name,
                slug: entity.entityType.slug,
            },
            categories: entity.entityCategories || [],
            tags: entity.entityTags || [],
            features: entity.entityFeatures || [],
            websiteUrl: entity.websiteUrl,
            logoUrl: entity.logoUrl,
            avgRating: entity.avgRating,
            reviewCount: entity.reviewCount,
        }));
    }
    async getRecommendedEntityDetails(recommendedIds, candidateEntities) {
        const recommendedEntities = recommendedIds
            .map((id) => candidateEntities.find((entity) => entity.id === id))
            .filter((entity) => entity !== undefined);
        return recommendedEntities.map((entity) => this.mapToEntityListItemResponseDto(entity));
    }
    mapToEntityListItemResponseDto(entity) {
        const listItemDto = new entity_list_item_response_dto_1.EntityListItemResponseDto();
        listItemDto.id = entity.id;
        listItemDto.name = entity.name;
        listItemDto.slug = entity.slug;
        listItemDto.logoUrl = entity.logoUrl;
        listItemDto.shortDescription = entity.shortDescription;
        listItemDto.websiteUrl = entity.websiteUrl;
        listItemDto.entityType = {
            name: entity.entityType.name,
            slug: entity.entityType.slug,
        };
        listItemDto.avgRating = entity.avgRating;
        listItemDto.reviewCount = entity.reviewCount;
        listItemDto.saveCount = entity._count?.userSavedEntities ?? 0;
        if (entity.entityType?.slug === 'ai-tool' && entity.entityDetailsTool) {
            listItemDto.hasFreeTier = entity.entityDetailsTool.hasFreeTier;
        }
        return listItemDto;
    }
    async getCurrentLlmProvider() {
        try {
            const providers = this.llmFactoryService.getAvailableProviders();
            return 'OPENAI';
        }
        catch (error) {
            this.logger.warn('Could not determine current LLM provider', error.message);
            return 'UNKNOWN';
        }
    }
    async getFallbackCandidates(problemDescription, originalFilters, maxCandidates) {
        this.logger.debug('Executing fallback candidate search strategies');
        let candidates = await this.tryLowerThresholdVectorSearch(problemDescription, maxCandidates);
        if (candidates.length > 0) {
            this.logger.debug(`Fallback Strategy 1 (lower threshold): Found ${candidates.length} candidates`);
            return candidates;
        }
        if (originalFilters.entityTypeIds?.length || originalFilters.entity_type_ids?.length) {
            candidates = await this.tryWithoutEntityTypeFilters(problemDescription, originalFilters, maxCandidates);
            if (candidates.length > 0) {
                this.logger.debug(`Fallback Strategy 2 (no entity type filter): Found ${candidates.length} candidates`);
                return candidates;
            }
        }
        candidates = await this.tryKeywordBasedSearch(problemDescription, originalFilters, maxCandidates);
        if (candidates.length > 0) {
            this.logger.debug(`Fallback Strategy 3 (keyword search): Found ${candidates.length} candidates`);
            return candidates;
        }
        candidates = await this.tryMinimalFilters(problemDescription, maxCandidates);
        if (candidates.length > 0) {
            this.logger.debug(`Fallback Strategy 4 (minimal filters): Found ${candidates.length} candidates`);
            return candidates;
        }
        this.logger.warn('All fallback strategies failed to find candidates');
        return [];
    }
    async tryLowerThresholdVectorSearch(problemDescription, maxCandidates) {
        try {
            const vectorResults = await this.entitiesService.vectorSearch({
                query: problemDescription,
                limit: maxCandidates * 5,
            });
            if (vectorResults.length === 0) {
                return [];
            }
            const entityIds = vectorResults.map(result => result.id);
            const result = await this.entitiesService.findAll({
                limit: maxCandidates * 2,
                page: 1,
                status: 'ACTIVE',
            });
            return entityIds
                .map(id => result.data.find(entity => entity.id === id))
                .filter(entity => entity !== undefined)
                .slice(0, maxCandidates);
        }
        catch (error) {
            this.logger.warn('Lower threshold vector search failed', error.message);
            return [];
        }
    }
    async tryWithoutEntityTypeFilters(problemDescription, originalFilters, maxCandidates) {
        try {
            const vectorResults = await this.entitiesService.vectorSearch({
                query: problemDescription,
                limit: maxCandidates * 3,
            });
            if (vectorResults.length === 0) {
                return [];
            }
            const entityIds = vectorResults.map(result => result.id);
            const filtersWithoutEntityType = { ...originalFilters };
            delete filtersWithoutEntityType.entityTypeIds;
            delete filtersWithoutEntityType.entity_type_ids;
            const result = await this.entitiesService.findAll({
                limit: maxCandidates * 2,
                page: 1,
                ...filtersWithoutEntityType,
            });
            return entityIds
                .map(id => result.data.find(entity => entity.id === id))
                .filter(entity => entity !== undefined)
                .slice(0, maxCandidates);
        }
        catch (error) {
            this.logger.warn('Search without entity type filters failed', error.message);
            return [];
        }
    }
    async tryKeywordBasedSearch(problemDescription, originalFilters, maxCandidates) {
        try {
            const keywords = this.extractKeywords(problemDescription);
            if (keywords.length === 0) {
                return [];
            }
            const searchTerm = keywords[0];
            const result = await this.entitiesService.findAll({
                limit: maxCandidates * 2,
                page: 1,
                searchTerm,
                status: 'ACTIVE',
                ...originalFilters,
            });
            return result.data.slice(0, maxCandidates);
        }
        catch (error) {
            this.logger.warn('Keyword-based search failed', error.message);
            return [];
        }
    }
    async tryMinimalFilters(problemDescription, maxCandidates) {
        try {
            const extractedFilters = await this.filterExtractionService.extractFiltersFromDescription(problemDescription);
            const result = await this.entitiesService.findAll({
                limit: maxCandidates * 2,
                page: 1,
                status: 'ACTIVE',
                entityTypeIds: extractedFilters.entityTypeIds,
            });
            return result.data.slice(0, maxCandidates);
        }
        catch (error) {
            this.logger.warn('Minimal filters search failed', error.message);
            return [];
        }
    }
    extractKeywords(description) {
        const stopWords = new Set(['i', 'need', 'want', 'looking', 'for', 'to', 'help', 'me', 'with', 'a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'by', 'from']);
        return description
            .toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 2 && !stopWords.has(word))
            .slice(0, 5);
    }
    async debugRecommendationPipeline(problemDescription) {
        const debug = {
            step1_embedding_generation: null,
            step2_vector_search: null,
            step3_embedding_coverage: null,
            step4_filter_extraction: null,
            step5_basic_search: null,
        };
        try {
            debug.step1_embedding_generation = await this.entitiesService.debugEmbeddingGeneration(problemDescription);
            debug.step2_vector_search = await this.entitiesService.vectorSearch({
                query: problemDescription,
                limit: 10,
            });
            debug.step3_embedding_coverage = await this.entitiesService.getEmbeddingCoverage();
            debug.step4_filter_extraction = await this.filterExtractionService.extractFiltersFromDescription(problemDescription);
            const basicSearch = await this.entitiesService.findAll({
                limit: 10,
                page: 1,
                status: 'ACTIVE',
            });
            debug.step5_basic_search = {
                total_entities: basicSearch.total,
                returned_entities: basicSearch.data.length,
                sample_entities: basicSearch.data.slice(0, 3).map(e => ({ id: e.id, name: e.name, entityTypeId: e.entityTypeId })),
            };
            return debug;
        }
        catch (error) {
            return {
                ...debug,
                error: error.message,
                stack: error.stack,
            };
        }
    }
    enhanceFiltersWithQueryAnalysis(extractedFilters, queryAnalysis) {
        const enhanced = { ...extractedFilters };
        if (!enhanced.entityTypeIds && queryAnalysis.concepts.entityTypes.length > 0) {
            enhanced.entity_type_ids = queryAnalysis.concepts.entityTypes;
        }
        if (queryAnalysis.concepts.technologies.length > 0) {
            const techTerms = queryAnalysis.concepts.technologies.join(' ');
            enhanced.key_features_search = enhanced.key_features_search
                ? `${enhanced.key_features_search} ${techTerms}`
                : techTerms;
        }
        if (queryAnalysis.concepts.useCases.length > 0) {
            const useCaseTerms = queryAnalysis.concepts.useCases.join(' ');
            enhanced.use_cases_search = enhanced.use_cases_search
                ? `${enhanced.use_cases_search} ${useCaseTerms}`
                : useCaseTerms;
        }
        if (queryAnalysis.implicitNeeds.budgetSensitive) {
            if (!enhanced.price_range) {
                enhanced.price_range = 'FREE,FREEMIUM';
            }
        }
        if (queryAnalysis.implicitNeeds.easeOfUse && queryAnalysis.primaryIntent.techLevel === 'beginner') {
            enhanced.technical_levels = enhanced.technical_levels || ['BEGINNER', 'INTERMEDIATE'];
        }
        return enhanced;
    }
    async findCandidateEntitiesWithQueryVariants(originalQuery, queryVariants, filters, maxCandidates) {
        this.logger.debug(`Finding candidates with ${queryVariants.length} query variants`);
        let candidateEntities = await this.findCandidateEntities(originalQuery, filters, maxCandidates);
        if (candidateEntities.length >= Math.min(maxCandidates * 0.7, 20)) {
            this.logger.debug(`Original query found sufficient candidates: ${candidateEntities.length}`);
            return candidateEntities;
        }
        const allCandidates = new Map();
        candidateEntities.forEach(entity => allCandidates.set(entity.id, entity));
        const variantsToTry = queryVariants.slice(1, 4);
        for (const variant of variantsToTry) {
            if (allCandidates.size >= maxCandidates)
                break;
            this.logger.debug(`Trying query variant: "${variant}"`);
            try {
                const variantCandidates = await this.findCandidateEntities(variant, filters, Math.max(10, maxCandidates - allCandidates.size));
                variantCandidates.forEach(entity => {
                    if (!allCandidates.has(entity.id)) {
                        allCandidates.set(entity.id, entity);
                    }
                });
                this.logger.debug(`Variant "${variant}" added ${variantCandidates.length} candidates, total: ${allCandidates.size}`);
            }
            catch (error) {
                this.logger.warn(`Query variant "${variant}" failed:`, error.message);
            }
        }
        const finalCandidates = Array.from(allCandidates.values()).slice(0, maxCandidates);
        this.logger.log(`Query variants search completed: ${finalCandidates.length} total candidates from ${queryVariants.length} variants`);
        return finalCandidates;
    }
    calculateDiversityWeight(queryIntent) {
        if (queryIntent.primary === 'exploration') {
            return 0.4;
        }
        if (queryIntent.primary === 'specific_need') {
            return 0.2;
        }
        if (queryIntent.primary === 'comparison') {
            return 0.3;
        }
        return 0.25;
    }
    async trackRecommendationInteraction(userId, sessionId, query, recommendedEntityIds, clickedEntityId, position, dwellTime, userAgent) {
        try {
            await this.feedbackService.trackInteraction({
                userId,
                sessionId,
                query,
                recommendedEntityIds,
                clickedEntityId,
                position,
                dwellTime,
                userAgent,
                timestamp: new Date(),
            });
            this.logger.debug(`Tracked recommendation interaction for query: "${query}"`);
        }
        catch (error) {
            this.logger.error('Error tracking recommendation interaction:', error.stack);
        }
    }
    async getPersonalizedRecommendations(createRecommendationDto, userId) {
        const baseRecommendations = await this.getRecommendations(createRecommendationDto);
        if (userId && baseRecommendations.recommended_entities.length > 0) {
            try {
                const personalizedEntities = await Promise.all(baseRecommendations.recommended_entities.map(async (entity) => {
                    const personalizedBoost = await this.feedbackService.getPersonalizedBoost(userId, entity.id);
                    return {
                        ...entity,
                        personalizedScore: (entity.avgRating || 2.5) * personalizedBoost,
                        personalizedBoost,
                    };
                }));
                personalizedEntities.sort((a, b) => (b.personalizedScore || 0) - (a.personalizedScore || 0));
                return {
                    ...baseRecommendations,
                    recommended_entities: personalizedEntities,
                    explanation: `${baseRecommendations.explanation}\n\n🎯 Results personalized based on your preferences and interaction history.`,
                    metadata: {
                        ...baseRecommendations.metadata,
                        personalization_applied: true,
                    },
                };
            }
            catch (error) {
                this.logger.warn('Error applying personalization, returning base recommendations:', error.message);
            }
        }
        return baseRecommendations;
    }
    async getQualityMetrics(timeWindowDays = 7) {
        try {
            return await this.feedbackService.getQualityMetrics(timeWindowDays);
        }
        catch (error) {
            this.logger.error('Error getting quality metrics:', error.stack);
            return {
                clickThroughRate: 0,
                averagePosition: 0,
                zeroClickRate: 0,
                averageDwellTime: 0,
                totalInteractions: 0,
            };
        }
    }
};
exports.RecommendationsService = RecommendationsService;
exports.RecommendationsService = RecommendationsService = RecommendationsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)('ILlmService')),
    __metadata("design:paramtypes", [entities_service_1.EntitiesService, Object, llm_factory_service_1.LlmFactoryService,
        filter_extraction_service_1.FilterExtractionService,
        query_understanding_service_1.QueryUnderstandingService,
        contextual_reranking_service_1.ContextualRerankingService,
        recommendation_feedback_service_1.RecommendationFeedbackService,
        advanced_entity_ranking_service_1.AdvancedEntityRankingService,
        performance_optimization_service_1.PerformanceOptimizationService,
        query_optimization_service_1.QueryOptimizationService])
], RecommendationsService);
//# sourceMappingURL=recommendations.service.js.map