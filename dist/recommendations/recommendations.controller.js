"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecommendationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const throttler_1 = require("@nestjs/throttler");
const supabase_auth_guard_1 = require("../auth/guards/supabase-auth.guard");
const recommendations_service_1 = require("./recommendations.service");
const create_recommendation_dto_1 = require("./dto/create-recommendation.dto");
const recommendation_response_dto_1 = require("./dto/recommendation-response.dto");
let RecommendationsController = class RecommendationsController {
    constructor(recommendationsService) {
        this.recommendationsService = recommendationsService;
    }
    async getRecommendations(createRecommendationDto) {
        return this.recommendationsService.getRecommendations(createRecommendationDto);
    }
    async debugRecommendations(body) {
        return this.recommendationsService.debugRecommendationPipeline(body.problem_description);
    }
};
exports.RecommendationsController = RecommendationsController;
__decorate([
    (0, common_1.Post)(),
    (0, throttler_1.Throttle)({ default: { limit: 10, ttl: 60000 } }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Get AI-powered recommendations with comprehensive filtering',
        description: `
    Get personalized AI recommendations based on a problem description with access to 80+ filter parameters.

    This enhanced endpoint:
    1. Uses vector search to find semantically relevant entities
    2. Applies comprehensive filters across all entity types (Tools, Courses, Jobs, Events, Hardware, etc.)
    3. Uses the configured LLM provider to analyze and recommend the best options
    4. Returns detailed recommendations with explanations

    **NEW: Enhanced Filtering Capabilities**
    - Tool filters: technical_levels, learning_curves, has_api, frameworks, platforms, etc.
    - Course filters: skill_levels, certificate_available, instructor_name, duration, etc.
    - Job filters: employment_types, experience_levels, salary ranges, location_types, etc.
    - Event filters: event_types, is_online, location, date ranges, registration_required, etc.
    - Hardware filters: hardware_types, manufacturers, price ranges, memory/processor specs, etc.
    - And 60+ more entity-specific filters for comprehensive discovery

    The LLM provider can be configured by admins via the admin settings API.
    `,
    }),
    (0, swagger_1.ApiBody)({
        type: create_recommendation_dto_1.CreateRecommendationDto,
        description: 'Problem description and comprehensive filters for precise recommendations',
        examples: {
            'Beginner AI Tool with API': {
                summary: 'AI tool for beginners with API access',
                value: {
                    problem_description: 'I need an AI tool to help me generate code documentation automatically for my Python projects',
                    filters: {
                        entityTypeIds: ['ai-tool'],
                        technical_levels: ['BEGINNER', 'INTERMEDIATE'],
                        has_api: true,
                        has_free_tier: true,
                        frameworks: ['Python'],
                        platforms: ['Web', 'Linux'],
                        use_cases_search: 'documentation',
                        max_candidates: 15,
                    },
                },
            },
            'Senior ML Job Remote': {
                summary: 'Senior ML engineering job, remote, high salary',
                value: {
                    problem_description: 'I want to find a senior machine learning engineering position that pays well and allows remote work',
                    filters: {
                        entityTypeIds: ['job'],
                        experience_levels: ['SENIOR', 'LEAD'],
                        employment_types: ['FULL_TIME'],
                        location_types: ['Remote'],
                        salary_min: 120,
                        job_description: 'machine learning',
                        max_candidates: 20,
                    },
                },
            },
            'AI Course with Certificate': {
                summary: 'AI course for beginners with certificate',
                value: {
                    problem_description: 'I am a beginner and want to learn about artificial intelligence with a certificate',
                    filters: {
                        entityTypeIds: ['course'],
                        skill_levels: ['BEGINNER'],
                        certificate_available: true,
                        searchTerm: 'artificial intelligence',
                        duration_text: 'weeks',
                        max_candidates: 10,
                    },
                },
            },
            'AI Conference Online 2024': {
                summary: 'Online AI conferences in 2024',
                value: {
                    problem_description: 'I want to attend AI conferences to learn about the latest developments',
                    filters: {
                        entityTypeIds: ['event'],
                        event_types: ['Conference'],
                        is_online: true,
                        start_date_from: '2024-01-01',
                        start_date_to: '2024-12-31',
                        searchTerm: 'artificial intelligence',
                        max_candidates: 15,
                    },
                },
            },
            'GPU Under $2000': {
                summary: 'GPU for AI development under $2000',
                value: {
                    problem_description: 'I need a powerful GPU for training machine learning models on a budget',
                    filters: {
                        entityTypeIds: ['hardware'],
                        hardware_types: ['GPU'],
                        price_max: 2000,
                        memory_search: '16GB',
                        manufacturers: ['NVIDIA'],
                        max_candidates: 10,
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'AI recommendations generated successfully',
        type: recommendation_response_dto_1.RecommendationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid request data',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Authentication required',
    }),
    (0, swagger_1.ApiResponse)({
        status: 429,
        description: 'Rate limit exceeded',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error during recommendation generation',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_recommendation_dto_1.CreateRecommendationDto]),
    __metadata("design:returntype", Promise)
], RecommendationsController.prototype, "getRecommendations", null);
__decorate([
    (0, common_1.Post)('debug'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Debug recommendation pipeline',
        description: 'Debug endpoint to check each step of the recommendation pipeline',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Debug information returned successfully',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], RecommendationsController.prototype, "debugRecommendations", null);
exports.RecommendationsController = RecommendationsController = __decorate([
    (0, swagger_1.ApiTags)('Recommendations'),
    (0, common_1.Controller)('recommendations'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.SupabaseAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [recommendations_service_1.RecommendationsService])
], RecommendationsController);
//# sourceMappingURL=recommendations.controller.js.map