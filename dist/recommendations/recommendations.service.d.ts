import { EntitiesService } from '../entities/entities.service';
import { ILlmService } from '../common/llm/interfaces/llm.service.interface';
import { LlmFactoryService } from '../common/llm/services/llm-factory.service';
import { CreateRecommendationDto } from './dto/create-recommendation.dto';
import { RecommendationResponseDto } from './dto/recommendation-response.dto';
import { FilterExtractionService } from './services/filter-extraction.service';
import { AdvancedEntityRankingService } from '../common/ranking/advanced-entity-ranking.service';
import { PerformanceOptimizationService } from '../common/performance/performance-optimization.service';
import { QueryOptimizationService } from '../common/performance/query-optimization.service';
export declare class RecommendationsService {
    private readonly entitiesService;
    private readonly llmService;
    private readonly llmFactoryService;
    private readonly filterExtractionService;
    private readonly advancedRankingService;
    private readonly performanceOptimizationService;
    private readonly queryOptimizationService;
    private readonly logger;
    constructor(entitiesService: EntitiesService, llmService: ILlmService, llmFactoryService: LlmFactoryService, filterExtractionService: FilterExtractionService, advancedRankingService: AdvancedEntityRankingService, performanceOptimizationService: PerformanceOptimizationService, queryOptimizationService: QueryOptimizationService);
    getRecommendations(createRecommendationDto: CreateRecommendationDto): Promise<RecommendationResponseDto>;
    private findCandidateEntities;
    private mergeFilters;
    private extractFilterConfidence;
    private extractUserPreferences;
    private generateQuerySignature;
    private convertToLlmCandidates;
    private getRecommendedEntityDetails;
    private mapToEntityListItemResponseDto;
    private getCurrentLlmProvider;
    private getFallbackCandidates;
    private tryLowerThresholdVectorSearch;
    private tryWithoutEntityTypeFilters;
    private tryKeywordBasedSearch;
    private tryMinimalFilters;
    private extractKeywords;
    debugRecommendationPipeline(problemDescription: string): Promise<any>;
}
