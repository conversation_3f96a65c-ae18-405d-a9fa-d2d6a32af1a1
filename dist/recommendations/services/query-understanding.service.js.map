{"version": 3, "file": "query-understanding.service.js", "sourceRoot": "", "sources": ["../../../src/recommendations/services/query-understanding.service.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAAoD;AAqC7C,IAAM,yBAAyB,iCAA/B,MAAM,yBAAyB;IAA/B;QACY,WAAM,GAAG,IAAI,eAAM,CAAC,2BAAyB,CAAC,IAAI,CAAC,CAAC;QAGpD,uBAAkB,GAAG;YACpC,SAAS,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,aAAa,EAAE,UAAU,CAAC;YAC7E,QAAQ,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC;YAC5E,QAAQ,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,kBAAkB,EAAE,YAAY,CAAC;YACzE,UAAU,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;YACpE,UAAU,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,aAAa,EAAE,QAAQ,CAAC;YAC5D,gBAAgB,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC;YAC1E,KAAK,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,CAAC;YAClE,OAAO,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC;YAChE,SAAS,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC;YAClD,WAAW,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC;YACvD,OAAO,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,CAAC;YACpD,YAAY,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC;SAC1D,CAAC;QAGe,uBAAkB,GAAG;YACpC,kBAAkB,EAAE,CAAC,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,yBAAyB,CAAC;YAC/E,eAAe,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC;YACjF,KAAK,EAAE,CAAC,KAAK,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,gBAAgB,CAAC;YACvE,iBAAiB,EAAE,CAAC,iBAAiB,EAAE,mBAAmB,EAAE,IAAI,EAAE,kBAAkB,CAAC;YACrF,YAAY,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC;YAC/D,WAAW,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC;YAClE,KAAK,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC;SAC5D,CAAC;QAGe,oBAAe,GAAG;YACjC,kBAAkB,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC;YACrE,kBAAkB,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;YAClE,eAAe,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;YACtD,eAAe,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC;YAC5D,kBAAkB,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC;YAC3D,WAAW,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,CAAC;YAClE,WAAW,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC;YAC7D,aAAa,EAAE,CAAC,aAAa,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,CAAC;SACpE,CAAC;IAmOJ,CAAC;IA9NC,KAAK,CAAC,qBAAqB,CAAC,aAAqB;QAC/C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,aAAa,GAAG,CAAC,CAAC;QAEzD,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YAG5D,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAGrD,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;YAG5E,MAAM,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;YAG1E,MAAM,cAAc,GAAG,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAE3F,MAAM,MAAM,GAAkB;gBAC5B,QAAQ,EAAE,aAAa;gBACvB,QAAQ;gBACR,aAAa;gBACb,cAAc;gBACd,aAAa;gBACb,aAAa;aACd,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBAC7C,MAAM,EAAE,aAAa,CAAC,OAAO;gBAC7B,YAAY,EAAE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM;gBACnD,YAAY,EAAE,cAAc,CAAC,MAAM;aACpC,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,aAAa,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAG5E,OAAO;gBACL,QAAQ,EAAE,aAAa;gBACvB,QAAQ,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE;gBAC5G,aAAa,EAAE,EAAE,eAAe,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE;gBACzI,cAAc,EAAE,CAAC,aAAa,CAAC;gBAC/B,aAAa,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,EAAE;gBAC1G,aAAa,EAAE,EAAE;aAClB,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,iBAAiB,CAAC,KAAa;QACrC,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAGvC,MAAM,cAAc,GAAG;YACrB,UAAU,EAAE,yDAAyD;YACrE,aAAa,EAAE,qDAAqD;YACpE,QAAQ,EAAE,iDAAiD;YAC3D,cAAc,EAAE,qDAAqD;SACtE,CAAC;QAEF,IAAI,OAAO,GAA2B,aAAa,CAAC;QACpD,IAAI,UAAU,GAAG,GAAG,CAAC;QAGrB,KAAK,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;YAC/D,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7B,OAAO,GAAG,MAAgC,CAAC;gBAC3C,UAAU,GAAG,GAAG,CAAC;gBACjB,MAAM;YACR,CAAC;QACH,CAAC;QAGD,IAAI,OAAO,GAA2B,UAAU,CAAC;QACjD,IAAI,0CAA0C,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAChE,OAAO,GAAG,WAAW,CAAC;YACtB,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;aAAM,IAAI,wCAAwC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACrE,OAAO,GAAG,eAAe,CAAC;QAC5B,CAAC;QAGD,IAAI,SAAS,GAA6B,cAAc,CAAC;QACzD,IAAI,0CAA0C,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAChE,SAAS,GAAG,UAAU,CAAC;QACzB,CAAC;aAAM,IAAI,wDAAwD,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACrF,SAAS,GAAG,UAAU,CAAC;QACzB,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,CAAC;IAC9E,CAAC;IAKO,eAAe,CAAC,KAAa;QACnC,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAEvC,MAAM,QAAQ,GAAsB;YAClC,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE,EAAE;YACd,QAAQ,EAAE,EAAE;YACZ,WAAW,EAAE,EAAE;SAChB,CAAC;QAGF,KAAK,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC7E,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;gBAC3D,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;QAGD,KAAK,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACvE,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;gBAC3D,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QAGD,KAAK,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;YACvE,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;gBAC3D,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QAGD,IAAI,iCAAiC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACvD,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;QACD,IAAI,uCAAuC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAC7D,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,0BAA0B,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAChD,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAKO,kBAAkB,CAAC,KAAa,EAAE,MAAmB;QAC3D,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAEvC,OAAO;YACL,eAAe,EAAE,gDAAgD,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,SAAS,KAAK,UAAU;YACrH,SAAS,EAAE,4CAA4C,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,SAAS,KAAK,UAAU;YAC3G,WAAW,EAAE,sCAAsC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,SAAS,KAAK,UAAU;YACvG,WAAW,EAAE,uCAAuC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,OAAO,KAAK,gBAAgB;YAC5G,OAAO,EAAE,6CAA6C,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,OAAO,KAAK,WAAW;YACzG,aAAa,EAAE,wCAAwC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,SAAS,KAAK,UAAU;SAC5G,CAAC;IACJ,CAAC;IAKO,qBAAqB,CAAC,KAAa,EAAE,QAA2B;QACtE,MAAM,aAAa,GAAa,EAAE,CAAC;QAGnC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACrD,aAAa,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;QAGH,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAClC,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACpD,aAAa,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAGH,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACvC,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC;aAC/B,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;aACxD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAClB,CAAC;IAKO,sBAAsB,CAC5B,aAAqB,EACrB,QAA2B,EAC3B,aAAuB;QAEvB,MAAM,QAAQ,GAAa,CAAC,aAAa,CAAC,CAAC;QAG3C,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YACxC,QAAQ,CAAC,IAAI,CAAC,GAAG,aAAa,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC;QAGH,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,aAAa,EAAE,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAGH,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAClC,QAAQ,CAAC,IAAI,CAAC,GAAG,aAAa,QAAQ,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAGH,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC,GAAG,aAAa,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC1D,CAAC;QAGD,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5C,CAAC;CACF,CAAA;AA3QY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;GACA,yBAAyB,CA2QrC"}