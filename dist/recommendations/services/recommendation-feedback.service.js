"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RecommendationFeedbackService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecommendationFeedbackService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
let RecommendationFeedbackService = RecommendationFeedbackService_1 = class RecommendationFeedbackService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(RecommendationFeedbackService_1.name);
    }
    async trackInteraction(interactionData) {
        try {
            this.logger.debug(`Tracking interaction for query: "${interactionData.query}"`);
            await this.prisma.recommendationInteraction.create({
                data: {
                    userId: interactionData.userId,
                    sessionId: interactionData.sessionId,
                    query: interactionData.query,
                    recommendedEntityIds: interactionData.recommendedEntityIds,
                    clickedEntityId: interactionData.clickedEntityId,
                    dwellTime: interactionData.dwellTime,
                    position: interactionData.position,
                    userAgent: interactionData.userAgent,
                    queryIntent: interactionData.queryIntent,
                    diversityScore: interactionData.diversityScore,
                    createdAt: interactionData.timestamp,
                },
            });
            if (interactionData.clickedEntityId) {
                await this.updateEntityScore(interactionData.clickedEntityId, 'click');
                if (interactionData.userId) {
                    await this.updateUserPreferences(interactionData.userId, interactionData.clickedEntityId);
                }
            }
            if (interactionData.clickedEntityId) {
                await this.updateQueryEntityAssociations(interactionData.query, interactionData.clickedEntityId);
            }
            this.logger.debug(`Interaction tracked successfully`);
        }
        catch (error) {
            this.logger.error('Error tracking interaction:', error.stack);
        }
    }
    async updateEntityScore(entityId, interactionType) {
        try {
            const scoreIncrement = this.getScoreIncrement(interactionType);
            await this.prisma.entity.update({
                where: { id: entityId },
                data: {
                    popularityScore: {
                        increment: scoreIncrement,
                    },
                    updatedAt: new Date(),
                },
            });
            this.logger.debug(`Updated entity ${entityId} score by ${scoreIncrement}`);
        }
        catch (error) {
            this.logger.warn(`Failed to update entity score for ${entityId}:`, error.message);
        }
    }
    getScoreIncrement(interactionType) {
        const scoreMap = {
            click: 1.0,
            view: 0.1,
            dwell: 0.5,
        };
        return scoreMap[interactionType] || 0;
    }
    async updateUserPreferences(userId, clickedEntityId) {
        try {
            const entity = await this.prisma.entity.findUnique({
                where: { id: clickedEntityId },
                include: {
                    entityType: true,
                    entityCategories: { include: { category: true } },
                    entityFeatures: { include: { feature: true } },
                },
            });
            if (!entity) {
                this.logger.warn(`Entity ${clickedEntityId} not found for preference update`);
                return;
            }
            const preferences = await this.prisma.userPreferences.upsert({
                where: { userId },
                create: {
                    userId,
                    preferredEntityTypes: [entity.entityType.slug],
                    preferredCategories: entity.entityCategories.map(ec => ec.category.slug),
                    preferredFeatures: entity.entityFeatures.map(ef => ef.feature.slug),
                    interactionCount: 1,
                    lastInteractionAt: new Date(),
                },
                update: {
                    preferredEntityTypes: {
                        push: entity.entityType.slug,
                    },
                    preferredCategories: {
                        push: entity.entityCategories.map(ec => ec.category.slug),
                    },
                    preferredFeatures: {
                        push: entity.entityFeatures.map(ef => ef.feature.slug),
                    },
                    interactionCount: {
                        increment: 1,
                    },
                    lastInteractionAt: new Date(),
                },
            });
            this.logger.debug(`Updated preferences for user ${userId}`);
        }
        catch (error) {
            this.logger.warn(`Failed to update user preferences for ${userId}:`, error.message);
        }
    }
    async updateQueryEntityAssociations(query, entityId) {
        try {
            const normalizedQuery = query.toLowerCase().trim();
            await this.prisma.queryEntityAssociation.upsert({
                where: {
                    query_entityId: {
                        query: normalizedQuery,
                        entityId,
                    },
                },
                create: {
                    query: normalizedQuery,
                    entityId,
                    clickCount: 1,
                    lastClickedAt: new Date(),
                },
                update: {
                    clickCount: {
                        increment: 1,
                    },
                    lastClickedAt: new Date(),
                },
            });
            this.logger.debug(`Updated query-entity association for "${normalizedQuery}" -> ${entityId}`);
        }
        catch (error) {
            this.logger.warn(`Failed to update query-entity association:`, error.message);
        }
    }
    async getPersonalizedBoost(userId, entityId) {
        if (!userId) {
            return 1.0;
        }
        try {
            const preferences = await this.prisma.userPreferences.findUnique({
                where: { userId },
            });
            if (!preferences) {
                return 1.0;
            }
            const entity = await this.prisma.entity.findUnique({
                where: { id: entityId },
                include: {
                    entityType: true,
                    entityCategories: { include: { category: true } },
                    entityFeatures: { include: { feature: true } },
                },
            });
            if (!entity) {
                return 1.0;
            }
            let boostScore = 1.0;
            if (preferences.preferredEntityTypes.includes(entity.entityType.slug)) {
                boostScore += 0.2;
            }
            const entityCategories = entity.entityCategories.map(ec => ec.category.slug);
            const categoryMatches = entityCategories.filter(cat => preferences.preferredCategories.includes(cat)).length;
            if (categoryMatches > 0) {
                boostScore += (categoryMatches / entityCategories.length) * 0.15;
            }
            const entityFeatures = entity.entityFeatures.map(ef => ef.feature.slug);
            const featureMatches = entityFeatures.filter(feat => preferences.preferredFeatures.includes(feat)).length;
            if (featureMatches > 0) {
                boostScore += (featureMatches / entityFeatures.length) * 0.1;
            }
            return Math.min(boostScore, 1.5);
        }
        catch (error) {
            this.logger.warn(`Failed to calculate personalized boost for user ${userId}:`, error.message);
            return 1.0;
        }
    }
    async getPopularEntities(limit = 10, timeWindowDays = 30) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - timeWindowDays);
            const popularEntities = await this.prisma.recommendationInteraction.groupBy({
                by: ['clickedEntityId'],
                where: {
                    clickedEntityId: { not: null },
                    createdAt: { gte: cutoffDate },
                },
                _count: {
                    clickedEntityId: true,
                },
                orderBy: {
                    _count: {
                        clickedEntityId: 'desc',
                    },
                },
                take: limit,
            });
            return popularEntities
                .filter(item => item.clickedEntityId !== null)
                .map(item => item.clickedEntityId);
        }
        catch (error) {
            this.logger.error('Error getting popular entities:', error.stack);
            return [];
        }
    }
    async getQualityMetrics(timeWindowDays = 7) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - timeWindowDays);
            const interactions = await this.prisma.recommendationInteraction.findMany({
                where: {
                    createdAt: { gte: cutoffDate },
                },
                select: {
                    clickedEntityId: true,
                    position: true,
                    dwellTime: true,
                },
            });
            const totalInteractions = interactions.length;
            const clickedInteractions = interactions.filter(i => i.clickedEntityId !== null);
            const clickThroughRate = totalInteractions > 0 ? clickedInteractions.length / totalInteractions : 0;
            const zeroClickRate = totalInteractions > 0 ?
                (totalInteractions - clickedInteractions.length) / totalInteractions : 0;
            const averagePosition = clickedInteractions.length > 0 ?
                clickedInteractions.reduce((sum, i) => sum + (i.position || 0), 0) / clickedInteractions.length : 0;
            const dwellTimes = interactions.filter(i => i.dwellTime !== null).map(i => i.dwellTime);
            const averageDwellTime = dwellTimes.length > 0 ?
                dwellTimes.reduce((sum, time) => sum + time, 0) / dwellTimes.length : 0;
            return {
                clickThroughRate,
                averagePosition,
                zeroClickRate,
                averageDwellTime,
                totalInteractions,
            };
        }
        catch (error) {
            this.logger.error('Error calculating quality metrics:', error.stack);
            return {
                clickThroughRate: 0,
                averagePosition: 0,
                zeroClickRate: 0,
                averageDwellTime: 0,
                totalInteractions: 0,
            };
        }
    }
    async shouldRetrainModel() {
        try {
            const recentInteractions = await this.prisma.recommendationInteraction.count({
                where: {
                    createdAt: {
                        gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
                    },
                },
            });
            return recentInteractions > 100;
        }
        catch (error) {
            this.logger.error('Error checking retrain condition:', error.stack);
            return false;
        }
    }
    async triggerModelRetraining() {
        this.logger.log('Model retraining triggered - this would integrate with your ML pipeline');
        try {
            await this.prisma.systemEvent.create({
                data: {
                    eventType: 'MODEL_RETRAIN_TRIGGERED',
                    description: 'Recommendation model retraining triggered based on interaction volume',
                    metadata: {
                        triggeredAt: new Date(),
                        reason: 'interaction_threshold_reached',
                    },
                },
            });
        }
        catch (error) {
            this.logger.warn('Failed to log retrain event:', error.message);
        }
    }
};
exports.RecommendationFeedbackService = RecommendationFeedbackService;
exports.RecommendationFeedbackService = RecommendationFeedbackService = RecommendationFeedbackService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], RecommendationFeedbackService);
//# sourceMappingURL=recommendation-feedback.service.js.map