"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var ContextualRerankingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextualRerankingService = void 0;
const common_1 = require("@nestjs/common");
let ContextualRerankingService = ContextualRerankingService_1 = class ContextualRerankingService {
    constructor() {
        this.logger = new common_1.Logger(ContextualRerankingService_1.name);
    }
    async rerankResults(entities, context, maxResults = 10) {
        if (entities.length === 0) {
            return entities;
        }
        this.logger.debug(`Re-ranking ${entities.length} entities with MMR and contextual factors`);
        try {
            const groupedEntities = this.groupByEntityType(entities);
            const diverseResults = this.applyMMR(entities, context, maxResults);
            const boostedResults = this.applyInteractionBoost(diverseResults, context);
            const timeAdjustedResults = this.applyTimeDecay(boostedResults);
            const qualityFilteredResults = this.applyQualityThresholds(timeAdjustedResults);
            this.logger.debug(`Re-ranking completed: ${entities.length} -> ${qualityFilteredResults.length} entities`);
            return qualityFilteredResults.slice(0, maxResults);
        }
        catch (error) {
            this.logger.error('Error in contextual re-ranking:', error.stack);
            return entities.slice(0, maxResults);
        }
    }
    groupByEntityType(entities) {
        const groups = new Map();
        entities.forEach(entity => {
            const entityType = entity.entityType?.name || entity.entityTypeSlug || 'unknown';
            if (!groups.has(entityType)) {
                groups.set(entityType, []);
            }
            groups.get(entityType).push(entity);
        });
        return groups;
    }
    applyMMR(entities, context, maxResults, lambda = 0.7) {
        if (entities.length <= 1) {
            return entities;
        }
        const selected = [];
        const remaining = [...entities];
        const adjustedLambda = this.adjustLambdaForContext(lambda, context);
        this.logger.debug(`Applying MMR with lambda=${adjustedLambda} for ${entities.length} entities`);
        while (remaining.length > 0 && selected.length < maxResults) {
            let bestScore = -Infinity;
            let bestIndex = -1;
            remaining.forEach((entity, index) => {
                const relevance = this.calculateRelevanceScore(entity, context);
                const diversity = this.calculateDiversityScore(entity, selected);
                const score = adjustedLambda * relevance + (1 - adjustedLambda) * diversity;
                if (score > bestScore) {
                    bestScore = score;
                    bestIndex = index;
                }
            });
            if (bestIndex !== -1) {
                selected.push(remaining[bestIndex]);
                remaining.splice(bestIndex, 1);
            }
            else {
                break;
            }
        }
        return selected;
    }
    adjustLambdaForContext(baseLambda, context) {
        let adjustedLambda = baseLambda;
        if (context.queryIntent?.primary === 'specific_need') {
            adjustedLambda += 0.1;
        }
        if (context.queryIntent?.primary === 'exploration') {
            adjustedLambda -= 0.1;
        }
        if (context.diversityWeight !== undefined) {
            adjustedLambda = 1 - context.diversityWeight;
        }
        return Math.max(0.3, Math.min(0.9, adjustedLambda));
    }
    calculateRelevanceScore(entity, context) {
        let score = 0;
        if (entity.similarity) {
            score += entity.similarity * 0.4;
        }
        if (entity.avgRating) {
            score += (entity.avgRating / 5) * 0.3;
        }
        if (entity.reviewCount) {
            const normalizedReviews = Math.min(entity.reviewCount / 100, 1);
            score += normalizedReviews * 0.2;
        }
        if (context.userContext?.technicalLevel && entity.technicalLevels) {
            const levelMatch = entity.technicalLevels.includes(context.userContext.technicalLevel);
            score += levelMatch ? 0.1 : 0;
        }
        return Math.max(0, Math.min(1, score));
    }
    calculateDiversityScore(entity, selectedEntities) {
        if (selectedEntities.length === 0) {
            return 1.0;
        }
        let diversityScore = 1.0;
        selectedEntities.forEach(selectedEntity => {
            if (entity.entityType?.name === selectedEntity.entityType?.name) {
                diversityScore -= 0.3;
            }
            const entityCategories = entity.categories?.map(c => c.category?.name) || [];
            const selectedCategories = selectedEntity.categories?.map(c => c.category?.name) || [];
            const categoryOverlap = entityCategories.filter(cat => selectedCategories.includes(cat)).length;
            if (categoryOverlap > 0) {
                diversityScore -= (categoryOverlap / Math.max(entityCategories.length, 1)) * 0.2;
            }
            const entityFeatures = entity.features?.map(f => f.feature?.name) || [];
            const selectedFeatures = selectedEntity.features?.map(f => f.feature?.name) || [];
            const featureOverlap = entityFeatures.filter(feat => selectedFeatures.includes(feat)).length;
            if (featureOverlap > 0) {
                diversityScore -= (featureOverlap / Math.max(entityFeatures.length, 1)) * 0.1;
            }
        });
        return Math.max(0, diversityScore);
    }
    applyInteractionBoost(entities, context) {
        if (!context.userContext?.previousInteractions) {
            return entities;
        }
        const previousInteractions = context.userContext.previousInteractions;
        return entities.map(entity => {
            let boostScore = 1.0;
            const entityCategories = entity.categories?.map(c => c.category?.name) || [];
            const hasInteractedCategory = entityCategories.some(cat => previousInteractions.some(interaction => interaction.includes(cat)));
            if (hasInteractedCategory) {
                boostScore += 0.1;
            }
            const entityType = entity.entityType?.name;
            const hasInteractedType = previousInteractions.some(interaction => interaction.includes(entityType));
            if (hasInteractedType) {
                boostScore += 0.05;
            }
            return {
                ...entity,
                interactionBoost: boostScore,
                adjustedScore: (entity.similarity || 0.5) * boostScore,
            };
        }).sort((a, b) => (b.adjustedScore || 0) - (a.adjustedScore || 0));
    }
    applyTimeDecay(entities) {
        const now = new Date();
        return entities.map(entity => {
            let timeDecayFactor = 1.0;
            if (entity.updatedAt) {
                const entityDate = new Date(entity.updatedAt);
                const daysSinceUpdate = (now.getTime() - entityDate.getTime()) / (1000 * 60 * 60 * 24);
                if (daysSinceUpdate > 365) {
                    timeDecayFactor = 0.9;
                }
                else if (daysSinceUpdate > 180) {
                    timeDecayFactor = 0.95;
                }
            }
            return {
                ...entity,
                timeDecayFactor,
                finalScore: (entity.adjustedScore || entity.similarity || 0.5) * timeDecayFactor,
            };
        }).sort((a, b) => (b.finalScore || 0) - (a.finalScore || 0));
    }
    applyQualityThresholds(entities) {
        return entities.filter(entity => {
            const minSimilarity = 0.1;
            if (entity.similarity && entity.similarity < minSimilarity) {
                return false;
            }
            if (entity.avgRating && entity.reviewCount > 5 && entity.avgRating < 2.0) {
                return false;
            }
            if (!entity.name || !entity.shortDescription) {
                return false;
            }
            return true;
        });
    }
    calculateResultSetDiversity(entities) {
        if (entities.length <= 1) {
            return 0;
        }
        const typeDistribution = new Map();
        entities.forEach(entity => {
            const entityType = entity.entityType?.name || 'unknown';
            typeDistribution.set(entityType, (typeDistribution.get(entityType) || 0) + 1);
        });
        let entropy = 0;
        const total = entities.length;
        typeDistribution.forEach(count => {
            const probability = count / total;
            entropy -= probability * Math.log2(probability);
        });
        const maxEntropy = Math.log2(typeDistribution.size);
        return maxEntropy > 0 ? entropy / maxEntropy : 0;
    }
};
exports.ContextualRerankingService = ContextualRerankingService;
exports.ContextualRerankingService = ContextualRerankingService = ContextualRerankingService_1 = __decorate([
    (0, common_1.Injectable)()
], ContextualRerankingService);
//# sourceMappingURL=contextual-reranking.service.js.map